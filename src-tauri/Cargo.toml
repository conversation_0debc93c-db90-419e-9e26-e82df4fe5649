[package]
name = "secure-password"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "secure_password_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2.5.0", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-sql = { version = "2.2.0", features = ["sqlite"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# 混合存储架构核心依赖 - 使用SeaORM + sqlx + rusqlite
sea-orm = { version = "1.1", features = ["sqlx-sqlite", "runtime-tokio-rustls", "macros", "with-chrono", "with-uuid"] }
sea-orm-migration = "1.1"
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
libsqlite3-sys = "0.30.1" # 确保rusqlite和sqlx使用相同的libsqlite3-sys版本

reqwest = { version = "0.11", features = ["json"] }
url = "2.5"
async-trait = "0.1"
rusqlite = { version = "0.32", features = ["bundled"] } # 使用 bundled 特性简化部署
thiserror = "1.0" # 方便错误处理
argon2 = "0.5" # KDF
aes-gcm = "0.10" # AEAD 加密
rand = "0.8" # 随机数生成 (salt, nonce)
hex = "0.4" # 用于方便地转换字节和十六进制字符串 (或者用 base64)
base64 = "0.22" # 用于存储二进制数据到数据库
sha2 = "0.10" # SHA-256 哈希算法，用于生成联系方式相关盐值
once_cell = "1.19" # 用于安全地初始化全局状态 (如数据库连接)
tokio = { version = "1", features = ["sync", "io-std", "full"] } # 用于 Mutex 和 Native Messaging IO
futures = "0.3"  # 用于 join_all 等异步工具函数
log = "0.4"
env_logger = "0.11" # 日志记录
zeroize = { version = "1.8.1", features = ["zeroize_derive"] } # 内存清理，添加 derive 特性
tauri-plugin-clipboard-manager = "2"
dotenv = "0.15"
chrono = { version = "0.4.40", features = ["serde"] }
tauri-plugin-os = "2"
tauri-plugin-http = "2"
anyhow = "1.0.98"
uuid = { version = "1.0", features = ["v4", "serde"] }
regex = "1.10" # 正则表达式验证
lazy_static = "1.4" # 静态变量初始化
machine-uid = "0.5" # 获取机器唯一标识

# 同步模块依赖
parking_lot = "0.12" # 高性能锁
dashmap = "5.5" # 并发HashMap
bincode = "1.3" # 二进制序列化
lz4_flex = "0.11" # 数据压缩
tempfile = "3.8" # 临时文件支持（用于测试和示例）

# 新的模块化加密系统依赖
keyring = { version = "2.3", optional = true } # 系统密钥链集成
subtle = "2.6" # 常时间比较，防止时序攻击
tauri-plugin-stronghold = "2"
ed25519-dalek = { version = "2.1", features = ["rand_core"] } # Ed25519 密钥对生成和签名
tauri-plugin-store = "2"

[features]
default = ["keyring"]
keyring = ["dep:keyring"]
tauri-os = []

[target."cfg(target_os = \"macos\")".dependencies]
cocoa = "0.26"
objc = { version = "0.2.7", features = [] }
# objc-sys = { version = "0.3.5", features = [] }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-single-instance = "2"


[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true

[profile.dev]
incremental = true

[profile.fast-release]
inherits = "release" # 继承 release 的配置
panic = "abort"      # 与 release 相同
codegen-units = 256  # 增加编译单元，提升编译速度
lto = false          # 禁用 LTO，提升编译速度
opt-level = 0        # 禁用优化，大幅提升编译速度
debug = true         # 保留调试信息
strip = false        # 不剥离符号，保留调试信息

[profile.fast-dev]
inherits = "dev"    # 继承 dev 的配置
codegen-units = 256 # 增加编译单元，提升编译速度
opt-level = 0       # 禁用优化，进一步提升编译速度
incremental = true  # 启用增量编译
debug = true        # 保留调试信息
strip = false       # 不剥离符号，保留调试信息

[dev-dependencies]
