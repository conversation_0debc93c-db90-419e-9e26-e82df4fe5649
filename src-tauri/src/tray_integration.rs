/// 托盘集成模块
///
/// 该模块负责将现有的托盘模块集成到当前客户端应用中，实现以下功能：
/// - 当用户点击应用窗口的关闭按钮时，应用不直接退出，而是最小化到系统托盘
/// - 在系统托盘图标上添加右键菜单，包含"显示主窗口"和"退出"选项
/// - 只有通过托盘菜单中的"退出"选项，才能真正关闭应用程序

use crate::tray::{
    TrayManager, TrayConfigBuilder, TrayMenu, TrayMenuBuilder,
    TrayEvent, TrayEventHandler, TrayEventType, TrayResult, TrayError
};
use tauri::{AppHand<PERSON>, Manager, WebviewWindow};
use std::sync::Arc;
use tokio::sync::Mutex;
use async_trait::async_trait;
use log::{info, warn, error};

/// 应用托盘集成管理器
///
/// 负责管理托盘与应用窗口之间的交互逻辑
pub struct AppTrayIntegration {
    /// 托盘管理器
    tray_manager: Arc<Mutex<TrayManager>>,
    /// Tauri 应用句柄
    app_handle: AppHandle,
    /// 主窗口名称
    main_window_name: String,
}

/// 托盘事件处理器
///
/// 处理托盘相关的事件，如菜单点击、图标点击等
struct AppTrayEventHandler {
    /// Tauri 应用句柄
    app_handle: AppHandle,
    /// 主窗口名称
    main_window_name: String,
}

impl AppTrayIntegration {
    /// 创建新的托盘集成实例
    ///
    /// # 参数
    ///
    /// * `app_handle` - Tauri 应用句柄
    /// * `main_window_name` - 主窗口名称，默认为 "main"
    ///
    /// # 返回值
    ///
    /// 返回托盘集成实例
    pub async fn new(
        app_handle: AppHandle,
        main_window_name: Option<String>,
    ) -> TrayResult<Self> {
        let main_window_name = main_window_name.unwrap_or_else(|| "main".to_string());

        info!("开始初始化应用托盘集成，主窗口名称: {}", main_window_name);

        // 创建托盘配置
        let config = TrayConfigBuilder::new()
            .title("Secure Password")
            .tooltip("安全密码管理器 - 右键查看选项")
            .icon_path("icons/32x32.png") // 使用现有的应用图标作为托盘图标
            .show_menu_on_right_click(true)
            .enable_double_click(true)
            .hide_to_tray_on_close(true) // 关闭时隐藏到托盘
            .build()?;

        // 创建托盘管理器
        let tray_manager = TrayManager::new(config).await?;

        // 创建事件处理器
        let event_handler = Box::new(AppTrayEventHandler::new(
            app_handle.clone(),
            main_window_name.clone(),
        ));

        // 注册事件处理器
        tray_manager.add_event_handler(event_handler).await?;

        // 创建并设置托盘菜单
        let menu = Self::create_tray_menu()?;
        tray_manager.set_menu(menu).await?;

        let tray_manager = Arc::new(Mutex::new(tray_manager));

        Ok(Self {
            tray_manager,
            app_handle,
            main_window_name,
        })
    }

    /// 初始化托盘
    ///
    /// 启动托盘并显示图标
    pub async fn initialize(&self) -> TrayResult<()> {
        info!("初始化托盘集成");

        let manager = self.tray_manager.lock().await;
        manager.initialize().await?;
        manager.show().await?;

        info!("托盘集成初始化完成");
        Ok(())
    }

    /// 创建托盘菜单
    ///
    /// 创建包含"显示主窗口"和"退出"选项的托盘菜单
    fn create_tray_menu() -> TrayResult<TrayMenu> {
        TrayMenuBuilder::new()
            .add_normal_item("show_window", "显示主窗口")?
            .add_separator()?
            .add_normal_item("quit", "退出")?
            .build()
    }

    /// 显示主窗口
    ///
    /// 恢复并显示主应用窗口
    pub async fn show_main_window(&self) -> TrayResult<()> {
        info!("显示主窗口: {}", self.main_window_name);

        if let Some(window) = self.app_handle.get_webview_window(&self.main_window_name) {
            // 如果窗口被最小化，先恢复
            if let Err(e) = window.unminimize() {
                warn!("恢复窗口失败: {}", e);
            }

            // 显示窗口
            if let Err(e) = window.show() {
                error!("显示窗口失败: {}", e);
                return Err(TrayError::internal(format!("显示窗口失败: {}", e)));
            }

            // 将窗口置于前台
            if let Err(e) = window.set_focus() {
                warn!("设置窗口焦点失败: {}", e);
            }

            info!("主窗口已显示");
        } else {
            error!("找不到主窗口: {}", self.main_window_name);
            return Err(TrayError::internal(format!("找不到主窗口: {}", self.main_window_name)));
        }

        Ok(())
    }

    /// 隐藏主窗口到托盘
    ///
    /// 隐藏主应用窗口，但保持应用运行
    pub async fn hide_to_tray(&self) -> TrayResult<()> {
        info!("隐藏主窗口到托盘");

        if let Some(window) = self.app_handle.get_webview_window(&self.main_window_name) {
            if let Err(e) = window.hide() {
                error!("隐藏窗口失败: {}", e);
                return Err(TrayError::internal(format!("隐藏窗口失败: {}", e)));
            }
            info!("主窗口已隐藏到托盘");
        } else {
            warn!("找不到主窗口: {}", self.main_window_name);
        }

        Ok(())
    }

    /// 退出应用程序
    ///
    /// 完全退出应用程序，清理所有资源
    pub async fn quit_application(&self) -> TrayResult<()> {
        info!("退出应用程序");

        // 销毁托盘
        {
            let manager = self.tray_manager.lock().await;
            if let Err(e) = manager.destroy().await {
                warn!("销毁托盘失败: {}", e);
            }
        }

        // 退出应用
        self.app_handle.exit(0);

        Ok(())
    }

    /// 获取托盘管理器的引用
    ///
    /// 用于高级操作或状态查询
    pub fn get_tray_manager(&self) -> Arc<Mutex<TrayManager>> {
        self.tray_manager.clone()
    }
}

impl AppTrayEventHandler {
    /// 创建新的事件处理器
    ///
    /// # 参数
    ///
    /// * `app_handle` - Tauri 应用句柄
    /// * `main_window_name` - 主窗口名称
    fn new(app_handle: AppHandle, main_window_name: String) -> Self {
        Self {
            app_handle,
            main_window_name,
        }
    }

    /// 处理菜单点击事件
    ///
    /// # 参数
    ///
    /// * `menu_id` - 被点击的菜单项ID
    async fn handle_menu_click(&self, menu_id: &str) -> TrayResult<()> {
        info!("处理托盘菜单点击: {}", menu_id);

        match menu_id {
            "show_window" => {
                self.show_main_window().await?;
            }
            "quit" => {
                self.quit_application().await?;
            }
            _ => {
                warn!("未知的菜单项ID: {}", menu_id);
            }
        }

        Ok(())
    }

    /// 显示主窗口
    async fn show_main_window(&self) -> TrayResult<()> {
        if let Some(window) = self.app_handle.get_webview_window(&self.main_window_name) {
            window.unminimize().map_err(|e| TrayError::internal(e.to_string()))?;
            window.show().map_err(|e| TrayError::internal(e.to_string()))?;
            window.set_focus().map_err(|e| TrayError::internal(e.to_string()))?;
        }
        Ok(())
    }

    /// 退出应用程序
    async fn quit_application(&self) -> TrayResult<()> {
        info!("用户请求退出应用");
        self.app_handle.exit(0);
        Ok(())
    }
}

#[async_trait]
impl TrayEventHandler for AppTrayEventHandler {
    /// 处理托盘事件
    ///
    /// # 参数
    ///
    /// * `event` - 托盘事件
    async fn handle_event(&self, event: &TrayEvent) -> TrayResult<()> {
        match &event.event_type {
            TrayEventType::MenuItemSelected => {
                if let Some(menu_id) = &event.menu_item_id {
                    self.handle_menu_click(menu_id).await?;
                }
            }
            TrayEventType::DoubleClick => {
                // 双击托盘图标显示主窗口
                info!("托盘图标被双击，显示主窗口");
                self.show_main_window().await?;
            }
            TrayEventType::LeftClick => {
                // 左键点击也显示主窗口
                info!("托盘图标被左键点击，显示主窗口");
                self.show_main_window().await?;
            }
            _ => {
                // 其他事件暂不处理
            }
        }

        Ok(())
    }

    /// 获取处理器名称
    fn name(&self) -> &str {
        "app_tray_event_handler"
    }

    /// 获取事件处理器优先级
    fn priority(&self) -> u32 {
        100 // 高优先级
    }
}

/// 设置窗口关闭行为
///
/// 配置主窗口在关闭时隐藏到托盘而不是退出应用
///
/// # 参数
///
/// * `window` - 要配置的窗口
/// * `tray_integration` - 托盘集成实例
pub fn setup_window_close_behavior(
    window: &WebviewWindow,
    tray_integration: Arc<AppTrayIntegration>,
) -> Result<(), Box<dyn std::error::Error>> {
    let window_label = window.label().to_string();
    info!("设置窗口关闭行为: {}", window_label);

    // 监听窗口关闭事件
    window.on_window_event({
        let tray_integration = tray_integration.clone();
        move |event| {
            if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                info!("窗口关闭请求被拦截，隐藏到托盘");

                // 阻止默认的关闭行为
                api.prevent_close();

                // 异步隐藏窗口到托盘
                let tray_integration = tray_integration.clone();
                tauri::async_runtime::spawn(async move {
                    if let Err(e) = tray_integration.hide_to_tray().await {
                        error!("隐藏窗口到托盘失败: {}", e);
                    }
                });
            }
        }
    });

    info!("窗口关闭行为设置完成");
    Ok(())
}

/// Tauri 命令：显示主窗口
///
/// 前端可以调用此命令来显示主窗口
#[tauri::command]
pub async fn show_main_window_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<(), String> {
    tray_integration
        .show_main_window()
        .await
        .map_err(|e| e.to_string())
}

/// Tauri 命令：隐藏主窗口到托盘
///
/// 前端可以调用此命令来隐藏主窗口到托盘
#[tauri::command]
pub async fn hide_to_tray_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<(), String> {
    tray_integration
        .hide_to_tray()
        .await
        .map_err(|e| e.to_string())
}

/// Tauri 命令：退出应用程序
///
/// 前端可以调用此命令来完全退出应用程序
#[tauri::command]
pub async fn quit_application_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<(), String> {
    tray_integration
        .quit_application()
        .await
        .map_err(|e| e.to_string())
}

/// Tauri 命令：检查托盘是否可用
///
/// 前端可以调用此命令来检查系统托盘是否可用
#[tauri::command]
pub async fn is_tray_available_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<bool, String> {
    let manager = tray_integration.get_tray_manager();
    let manager_guard = manager.lock().await;
    Ok(manager_guard.is_running())
}
