/// 托盘集成模块
///
/// 简化的托盘集成，提供基本的托盘功能：
/// - 窗口关闭时隐藏到托盘
/// - 托盘菜单：显示窗口、退出应用
use crate::tray::{TrayConfigBuilder, TrayManager, TrayMenuBuilder, TrayResult};
use std::sync::Arc;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WebviewWindow};
use tokio::sync::Mutex;

/// 简化的托盘集成管理器
pub struct AppTrayIntegration {
    tray_manager: Arc<Mutex<TrayManager>>,
    app_handle: AppHandle,
    main_window_name: String,
}

impl AppTrayIntegration {
    /// 创建并初始化托盘集成
    pub async fn new(app_handle: AppHandle, main_window_name: Option<String>) -> TrayResult<Self> {
        let main_window_name = main_window_name.unwrap_or_else(|| "main".to_string());

        // 创建托盘配置
        let config = TrayConfigBuilder::new()
            .title("Secure Password")
            .tooltip("安全密码管理器")
            .icon_path("icons/32x32.png")
            .build()?;

        // 创建托盘管理器
        let mut tray_manager = TrayManager::with_window_label(config, &main_window_name)?;

        // 创建托盘菜单
        let menu = TrayMenuBuilder::new()
            .add_normal_item("show_window", "显示主窗口")?
            .add_separator()?
            .add_normal_item("quit", "退出")?
            .build()?;

        // 设置菜单并初始化
        tray_manager.set_menu(menu)?;
        tray_manager.initialize(&app_handle).await?;
        tray_manager.show()?;

        Ok(Self {
            tray_manager: Arc::new(Mutex::new(tray_manager)),
            app_handle,
            main_window_name,
        })
    }

    /// 显示主窗口
    pub fn show_main_window(&self) -> TrayResult<()> {
        if let Some(window) = self.app_handle.get_webview_window(&self.main_window_name) {
            let _ = window.unminimize();
            let _ = window.show();
            let _ = window.set_focus();
        }
        Ok(())
    }

    /// 隐藏窗口到托盘
    pub fn hide_to_tray(&self) -> TrayResult<()> {
        if let Some(window) = self.app_handle.get_webview_window(&self.main_window_name) {
            let _ = window.hide();
        }
        Ok(())
    }

    /// 退出应用
    pub fn quit_application(&self) {
        self.app_handle.exit(0);
    }
}

/// 设置窗口关闭行为
pub fn setup_window_close_behavior(
    window: &WebviewWindow,
    tray_integration: Arc<AppTrayIntegration>,
) -> Result<(), Box<dyn std::error::Error>> {
    window.on_window_event({
        move |event| {
            if let tauri::WindowEvent::CloseRequested { api, .. } = event {
                api.prevent_close();
                let _ = tray_integration.hide_to_tray();
            }
        }
    });
    Ok(())
}

/// Tauri 命令：显示主窗口
#[tauri::command]
pub fn show_main_window_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<(), String> {
    tray_integration.show_main_window().map_err(|e| e.to_string())
}

/// Tauri 命令：隐藏到托盘
#[tauri::command]
pub fn hide_to_tray_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<(), String> {
    tray_integration.hide_to_tray().map_err(|e| e.to_string())
}

/// Tauri 命令：退出应用
#[tauri::command]
pub fn quit_application_command(
    tray_integration: tauri::State<'_, Arc<AppTrayIntegration>>,
) -> Result<(), String> {
    tray_integration.quit_application();
    Ok(())
}
