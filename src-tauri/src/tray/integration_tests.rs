/// Tray 模块集成测试
/// 
/// 测试整个 tray 模块的集成功能和端到端工作流程

#[cfg(test)]
mod integration_tests {
    use crate::tray::{
        TrayConfig, TrayManager, TrayManagerBuilder, TrayMenuItem, TrayMenuBuilder,
        TrayEventHandler, TrayEvent, TrayEventType, TrayError, TrayResult,
        TrayIcon, TrayIconManager, TrayIconType, TrayState, TrayStatus
    };
    use std::sync::Arc;
    use tokio::sync::Mutex;
    use std::time::Duration;

    /// 模拟事件处理器，用于测试
    struct MockEventHandler {
        events_received: Arc<Mutex<Vec<TrayEvent>>>,
    }

    impl MockEventHandler {
        fn new() -> Self {
            Self {
                events_received: Arc::new(Mutex::new(Vec::new())),
            }
        }

        async fn get_received_events(&self) -> Vec<TrayEvent> {
            self.events_received.lock().await.clone()
        }

        async fn clear_events(&self) {
            self.events_received.lock().await.clear();
        }
    }

    #[async_trait::async_trait]
    impl TrayEventHandler for MockEventHandler {
        async fn handle_event(&self, event: &TrayEvent) -> TrayResult<()> {
            self.events_received.lock().await.push(event.clone());
            Ok(())
        }
    }

    /// 创建测试用的托盘配置
    fn create_test_config() -> TrayResult<TrayConfig> {
        let menu = TrayMenuBuilder::new()
            .add_item(TrayMenuItem::new("test1", "测试项目1"))
            .add_item(TrayMenuItem::new("test2", "测试项目2"))
            .add_separator()
            .add_item(TrayMenuItem::new("quit", "退出"))
            .build()?;

        Ok(TrayConfig::builder()
            .title("测试托盘")
            .tooltip("这是一个测试托盘")
            .icon_path("test-icon.png")
            .menu(menu)
            .build())
    }

    #[tokio::test]
    async fn test_tray_manager_lifecycle() {
        // 创建配置
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        // 创建托盘管理器
        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler.clone())
            .build()
            .await;

        // 在某些测试环境中，托盘可能不被支持
        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 测试显示托盘
        assert!(tray_manager.show().await.is_ok());
        assert!(tray_manager.is_visible().await);

        // 测试隐藏托盘
        assert!(tray_manager.hide().await.is_ok());
        assert!(!tray_manager.is_visible().await);

        // 测试再次显示
        assert!(tray_manager.show().await.is_ok());
        assert!(tray_manager.is_visible().await);

        // 测试销毁
        assert!(tray_manager.destroy().await.is_ok());
    }

    #[tokio::test]
    async fn test_tray_icon_management() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 测试更新图标
        assert!(tray_manager.update_icon("new-icon.png").await.is_ok());
        
        // 测试更新工具提示
        assert!(tray_manager.update_tooltip("新的工具提示").await.is_ok());
        
        let current_tooltip = tray_manager.current_tooltip().await;
        assert_eq!(current_tooltip, Some("新的工具提示".to_string()));
    }

    #[tokio::test]
    async fn test_tray_menu_operations() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 测试菜单项数量
        let menu_count = tray_manager.menu_items_count().await;
        assert!(menu_count > 0);

        // 测试更新菜单
        let new_menu = TrayMenuBuilder::new()
            .add_item(TrayMenuItem::new("new1", "新项目1"))
            .add_item(TrayMenuItem::new("new2", "新项目2"))
            .build().unwrap();

        assert!(tray_manager.update_menu(new_menu).await.is_ok());
        
        let updated_count = tray_manager.menu_items_count().await;
        assert_eq!(updated_count, 2);
    }

    #[tokio::test]
    async fn test_tray_event_handling() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler.clone())
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 模拟事件
        let test_event = TrayEvent {
            event_type: TrayEventType::LeftClick,
            timestamp: std::time::SystemTime::now(),
            menu_item_id: None,
            position: Some((100, 200)),
            metadata: std::collections::HashMap::new(),
        };

        // 手动触发事件处理
        assert!(event_handler.handle_event(&test_event).await.is_ok());

        // 检查事件是否被正确处理
        let received_events = event_handler.get_received_events().await;
        assert_eq!(received_events.len(), 1);
        assert!(matches!(received_events[0].event_type, TrayEventType::LeftClick));
    }

    #[tokio::test]
    async fn test_tray_state_management() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 测试初始状态
        let initial_status = tray_manager.current_status().await;
        assert!(matches!(initial_status, TrayStatus::Hidden | TrayStatus::Visible));

        // 测试状态转换
        assert!(tray_manager.show().await.is_ok());
        let visible_status = tray_manager.current_status().await;
        assert!(matches!(visible_status, TrayStatus::Visible));

        assert!(tray_manager.hide().await.is_ok());
        let hidden_status = tray_manager.current_status().await;
        assert!(matches!(hidden_status, TrayStatus::Hidden));
    }

    #[tokio::test]
    async fn test_tray_statistics() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 获取初始统计信息
        let initial_stats = tray_manager.get_statistics().await;
        assert!(initial_stats.contains_key("creation_time"));

        // 执行一些操作
        assert!(tray_manager.show().await.is_ok());
        assert!(tray_manager.hide().await.is_ok());

        // 检查统计信息是否更新
        let updated_stats = tray_manager.get_statistics().await;
        assert!(updated_stats.contains_key("show_count"));
        assert!(updated_stats.contains_key("hide_count"));

        // 重置统计信息
        tray_manager.reset_statistics().await;
        let reset_stats = tray_manager.get_statistics().await;
        
        // 验证统计信息已重置（某些基本信息可能仍然存在）
        assert!(reset_stats.len() <= initial_stats.len());
    }

    #[tokio::test]
    async fn test_tray_notification() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 测试显示通知
        let result = tray_manager.show_notification("测试标题", "测试消息").await;
        
        // 在某些环境中通知可能不被支持，这是正常的
        if result.is_err() {
            println!("通知在当前环境中不被支持");
        } else {
            assert!(result.is_ok());
        }
    }

    #[tokio::test]
    async fn test_tray_blinking() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 测试开始闪烁
        let start_result = tray_manager.start_blinking().await;
        if start_result.is_ok() {
            // 等待一小段时间
            tokio::time::sleep(Duration::from_millis(100)).await;
            
            // 测试停止闪烁
            assert!(tray_manager.stop_blinking().await.is_ok());
        } else {
            println!("闪烁功能在当前环境中不被支持");
        }
    }

    #[tokio::test]
    async fn test_icon_manager_integration() {
        let mut icon_manager = TrayIconManager::new();

        // 测试加载图标
        let icon_data = vec![0u8; 100]; // 模拟图标数据
        let icon = TrayIcon::new(TrayIconType::Png, icon_data);
        
        let result = icon_manager.load_icon("test-icon", icon).await;
        assert!(result.is_ok());

        // 测试获取图标
        let retrieved_icon = icon_manager.get_icon("test-icon").await;
        assert!(retrieved_icon.is_some());

        // 测试缓存统计
        let stats = icon_manager.get_cache_stats().await;
        assert_eq!(stats.total_icons, 1);
        assert!(stats.cache_hits >= 0);
    }

    #[tokio::test]
    async fn test_error_handling() {
        // 测试无效配置
        let invalid_config = TrayConfig::builder()
            .title("")  // 空标题
            .build();

        let event_handler = Arc::new(MockEventHandler::new());

        let result = TrayManagerBuilder::new()
            .config(invalid_config)
            .event_handler(event_handler)
            .build()
            .await;

        // 应该返回配置错误
        if let Err(error) = result {
            assert!(matches!(error, TrayError::ConfigurationError(_)));
        }
    }

    #[tokio::test]
    async fn test_concurrent_operations() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = Arc::new(tray_manager.unwrap());

        // 并发执行多个操作
        let mut handles = Vec::new();

        for i in 0..5 {
            let manager = tray_manager.clone();
            let handle = tokio::spawn(async move {
                let tooltip = format!("并发测试 {}", i);
                manager.update_tooltip(&tooltip).await
            });
            handles.push(handle);
        }

        // 等待所有操作完成
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok());
        }
    }

    #[tokio::test]
    async fn test_resource_cleanup() {
        let config = create_test_config().unwrap();
        let event_handler = Arc::new(MockEventHandler::new());

        let tray_manager = TrayManagerBuilder::new()
            .config(config)
            .event_handler(event_handler)
            .build()
            .await;

        if tray_manager.is_err() {
            println!("托盘在当前环境中不被支持，跳过测试");
            return;
        }

        let tray_manager = tray_manager.unwrap();

        // 显示托盘
        assert!(tray_manager.show().await.is_ok());

        // 销毁托盘
        assert!(tray_manager.destroy().await.is_ok());

        // 尝试在销毁后操作应该失败
        let result = tray_manager.show().await;
        assert!(result.is_err());
    }
} 