/// 托盘配置模块
/// 
/// 提供托盘的配置选项和建造者模式的配置构建器

use crate::tray::{TrayError, TrayResult, DEFAULT_TRAY_ICON_SIZE};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::time::Duration;

/// 托盘配置
/// 
/// 包含托盘的所有配置选项，如标题、工具提示、图标等
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayConfig {
    /// 托盘标题
    pub title: String,
    
    /// 托盘工具提示文本
    pub tooltip: Option<String>,
    
    /// 托盘图标路径
    pub icon_path: Option<PathBuf>,
    
    /// 托盘图标数据（Base64编码）
    pub icon_data: Option<String>,
    
    /// 图标尺寸（宽度，高度）
    pub icon_size: (u32, u32),
    
    /// 是否在左键点击时显示菜单
    pub show_menu_on_left_click: bool,
    
    /// 是否在右键点击时显示菜单
    pub show_menu_on_right_click: bool,
    
    /// 是否启用双击事件
    pub enable_double_click: bool,
    
    /// 是否启用鼠标悬停事件
    pub enable_hover_events: bool,
    
    /// 事件处理超时时间
    pub event_timeout: Duration,
    
    /// 是否在应用启动时自动显示托盘
    pub auto_show_on_startup: bool,
    
    /// 是否在应用关闭时隐藏到托盘
    pub hide_to_tray_on_close: bool,
    
    /// 平台特定配置
    pub platform_config: PlatformConfig,
    
    /// 状态管理配置
    pub state_config: StateConfig,
    
    /// 图标缓存配置
    pub icon_cache_config: IconCacheConfig,
    
    /// 图标目录路径
    pub icon_directory: Option<PathBuf>,
}

/// 状态管理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateConfig {
    /// 是否启用状态持久化
    pub enable_persistence: bool,
    
    /// 状态文件路径
    pub state_file_path: Option<PathBuf>,
    
    /// 状态历史记录最大数量
    pub max_history_size: usize,
    
    /// 自动保存间隔
    pub auto_save_interval: Duration,
}

/// 图标缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IconCacheConfig {
    /// 缓存最大大小（字节）
    pub max_cache_size: usize,
    
    /// 缓存项最大数量
    pub max_cache_items: usize,
    
    /// 缓存过期时间
    pub cache_ttl: Duration,
    
    /// 是否启用预加载
    pub enable_preload: bool,
}

impl Default for StateConfig {
    fn default() -> Self {
        Self {
            enable_persistence: true,
            state_file_path: None,
            max_history_size: 100,
            auto_save_interval: Duration::from_secs(30),
        }
    }
}

impl Default for IconCacheConfig {
    fn default() -> Self {
        Self {
            max_cache_size: 10 * 1024 * 1024, // 10MB
            max_cache_items: 100,
            cache_ttl: Duration::from_secs(3600), // 1 hour
            enable_preload: true,
        }
    }
}

/// 平台特定配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformConfig {
    /// Windows 特定配置
    #[cfg(target_os = "windows")]
    pub windows: WindowsConfig,
    
    /// macOS 特定配置
    #[cfg(target_os = "macos")]
    pub macos: MacOSConfig,
    
    /// Linux 特定配置
    #[cfg(target_os = "linux")]
    pub linux: LinuxConfig,
}

/// Windows 平台配置
#[cfg(target_os = "windows")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowsConfig {
    /// 是否使用系统主题图标
    pub use_system_theme_icon: bool,
    
    /// 是否启用气球提示
    pub enable_balloon_tips: bool,
    
    /// 气球提示超时时间
    pub balloon_tip_timeout: Duration,
}

/// macOS 平台配置
#[cfg(target_os = "macos")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MacOSConfig {
    /// 是否使用模板图标（自动适应深色/浅色主题）
    pub use_template_icon: bool,
    
    /// 是否显示在菜单栏
    pub show_in_menu_bar: bool,
    
    /// 是否显示在 Dock
    pub show_in_dock: bool,
}

/// Linux 平台配置
#[cfg(target_os = "linux")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LinuxConfig {
    /// 桌面环境类型
    pub desktop_environment: DesktopEnvironment,
    
    /// 是否使用 AppIndicator
    pub use_app_indicator: bool,
    
    /// 是否启用状态图标
    pub enable_status_icon: bool,
}

/// Linux 桌面环境类型
#[cfg(target_os = "linux")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DesktopEnvironment {
    /// GNOME 桌面环境
    Gnome,
    /// KDE 桌面环境
    Kde,
    /// XFCE 桌面环境
    Xfce,
    /// Unity 桌面环境
    Unity,
    /// 其他桌面环境
    Other(String),
    /// 自动检测
    Auto,
}

impl Default for TrayConfig {
    fn default() -> Self {
        Self {
            title: "应用程序".to_string(),
            tooltip: None,
            icon_path: None,
            icon_data: None,
            icon_size: (DEFAULT_TRAY_ICON_SIZE, DEFAULT_TRAY_ICON_SIZE),
            show_menu_on_left_click: false,
            show_menu_on_right_click: true,
            enable_double_click: true,
            enable_hover_events: false,
            event_timeout: Duration::from_secs(5),
            auto_show_on_startup: true,
            hide_to_tray_on_close: false,
            platform_config: PlatformConfig::default(),
            state_config: StateConfig::default(),
            icon_cache_config: IconCacheConfig::default(),
            icon_directory: None,
        }
    }
}

impl Default for PlatformConfig {
    fn default() -> Self {
        Self {
            #[cfg(target_os = "windows")]
            windows: WindowsConfig::default(),
            #[cfg(target_os = "macos")]
            macos: MacOSConfig::default(),
            #[cfg(target_os = "linux")]
            linux: LinuxConfig::default(),
        }
    }
}

#[cfg(target_os = "windows")]
impl Default for WindowsConfig {
    fn default() -> Self {
        Self {
            use_system_theme_icon: true,
            enable_balloon_tips: true,
            balloon_tip_timeout: Duration::from_secs(3),
        }
    }
}

#[cfg(target_os = "macos")]
impl Default for MacOSConfig {
    fn default() -> Self {
        Self {
            use_template_icon: true,
            show_in_menu_bar: true,
            show_in_dock: false,
        }
    }
}

#[cfg(target_os = "linux")]
impl Default for LinuxConfig {
    fn default() -> Self {
        Self {
            desktop_environment: DesktopEnvironment::Auto,
            use_app_indicator: true,
            enable_status_icon: true,
        }
    }
}

/// 托盘配置建造者
/// 
/// 使用建造者模式来构建托盘配置，提供流畅的API
#[derive(Debug, Default)]
pub struct TrayConfigBuilder {
    config: TrayConfig,
}

impl TrayConfigBuilder {
    /// 创建新的配置建造者
    pub fn new() -> Self {
        Self {
            config: TrayConfig::default(),
        }
    }

    /// 设置托盘标题
    pub fn title(mut self, title: impl Into<String>) -> Self {
        self.config.title = title.into();
        self
    }

    /// 设置工具提示
    pub fn tooltip(mut self, tooltip: impl Into<String>) -> Self {
        self.config.tooltip = Some(tooltip.into());
        self
    }

    /// 设置图标路径
    pub fn icon_path(mut self, path: impl Into<PathBuf>) -> Self {
        self.config.icon_path = Some(path.into());
        self
    }

    /// 设置图标数据（Base64编码）
    pub fn icon_data(mut self, data: impl Into<String>) -> Self {
        self.config.icon_data = Some(data.into());
        self
    }

    /// 设置图标尺寸
    pub fn icon_size(mut self, width: u32, height: u32) -> Self {
        self.config.icon_size = (width, height);
        self
    }

    /// 设置是否在左键点击时显示菜单
    pub fn show_menu_on_left_click(mut self, show: bool) -> Self {
        self.config.show_menu_on_left_click = show;
        self
    }

    /// 设置是否在右键点击时显示菜单
    pub fn show_menu_on_right_click(mut self, show: bool) -> Self {
        self.config.show_menu_on_right_click = show;
        self
    }

    /// 设置是否启用双击事件
    pub fn enable_double_click(mut self, enable: bool) -> Self {
        self.config.enable_double_click = enable;
        self
    }

    /// 设置是否启用鼠标悬停事件
    pub fn enable_hover_events(mut self, enable: bool) -> Self {
        self.config.enable_hover_events = enable;
        self
    }

    /// 设置事件处理超时时间
    pub fn event_timeout(mut self, timeout: Duration) -> Self {
        self.config.event_timeout = timeout;
        self
    }

    /// 设置是否在应用启动时自动显示托盘
    pub fn auto_show_on_startup(mut self, auto_show: bool) -> Self {
        self.config.auto_show_on_startup = auto_show;
        self
    }

    /// 设置是否在启动时可见（别名方法）
    pub fn visible_on_startup(mut self, visible: bool) -> Self {
        self.config.auto_show_on_startup = visible;
        self
    }

    /// 设置是否在应用关闭时隐藏到托盘
    pub fn hide_to_tray_on_close(mut self, hide: bool) -> Self {
        self.config.hide_to_tray_on_close = hide;
        self
    }

    /// 设置平台特定配置
    pub fn platform_config(mut self, platform_config: PlatformConfig) -> Self {
        self.config.platform_config = platform_config;
        self
    }

    /// Windows 平台特定配置
    #[cfg(target_os = "windows")]
    pub fn windows_config(mut self, windows_config: WindowsConfig) -> Self {
        self.config.platform_config.windows = windows_config;
        self
    }

    /// macOS 平台特定配置
    #[cfg(target_os = "macos")]
    pub fn macos_config(mut self, macos_config: MacOSConfig) -> Self {
        self.config.platform_config.macos = macos_config;
        self
    }

    /// Linux 平台特定配置
    #[cfg(target_os = "linux")]
    pub fn linux_config(mut self, linux_config: LinuxConfig) -> Self {
        self.config.platform_config.linux = linux_config;
        self
    }

    /// 构建配置
    pub fn build(self) -> TrayResult<TrayConfig> {
        self.validate_config()?;
        Ok(self.config)
    }

    /// 验证配置的有效性
    fn validate_config(&self) -> TrayResult<()> {
        // 验证标题不为空
        if self.config.title.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "title",
                "标题不能为空",
            ));
        }

        // 验证图标配置
        if self.config.icon_path.is_none() && self.config.icon_data.is_none() {
            return Err(TrayError::configuration_error(
                "icon",
                "必须提供图标路径或图标数据",
            ));
        }

        // 验证图标尺寸
        let (width, height) = self.config.icon_size;
        if width == 0 || height == 0 {
            return Err(TrayError::configuration_error(
                "icon_size",
                "图标尺寸必须大于0",
            ));
        }

        // 验证超时时间
        if self.config.event_timeout.is_zero() {
            return Err(TrayError::configuration_error(
                "event_timeout",
                "事件超时时间必须大于0",
            ));
        }

        Ok(())
    }
}

impl TrayConfig {
    /// 创建配置建造者
    pub fn builder() -> TrayConfigBuilder {
        TrayConfigBuilder::new()
    }

    /// 从 JSON 字符串加载配置
    pub fn from_json(json: &str) -> TrayResult<Self> {
        serde_json::from_str(json).map_err(TrayError::from)
    }

    /// 将配置序列化为 JSON 字符串
    pub fn to_json(&self) -> TrayResult<String> {
        serde_json::to_string_pretty(self).map_err(TrayError::from)
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> TrayResult<()> {
        if self.title.trim().is_empty() {
            return Err(TrayError::configuration_error(
                "title",
                "标题不能为空",
            ));
        }

        if self.icon_path.is_none() && self.icon_data.is_none() {
            return Err(TrayError::configuration_error(
                "icon",
                "必须提供图标路径或图标数据",
            ));
        }

        let (width, height) = self.icon_size;
        if width == 0 || height == 0 {
            return Err(TrayError::configuration_error(
                "icon_size",
                "图标尺寸必须大于0",
            ));
        }

        if self.event_timeout.is_zero() {
            return Err(TrayError::configuration_error(
                "event_timeout",
                "事件超时时间必须大于0",
            ));
        }

        Ok(())
    }

    /// 获取当前平台的配置
    pub fn get_current_platform_config(&self) -> &PlatformConfig {
        &self.platform_config
    }

    /// 检查是否应该在指定的鼠标按钮点击时显示菜单
    pub fn should_show_menu_on_click(&self, is_left_click: bool) -> bool {
        if is_left_click {
            self.show_menu_on_left_click
        } else {
            self.show_menu_on_right_click
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = TrayConfig::default();
        assert_eq!(config.title, "应用程序");
        assert_eq!(config.icon_size, (DEFAULT_TRAY_ICON_SIZE, DEFAULT_TRAY_ICON_SIZE));
        assert!(!config.show_menu_on_left_click);
        assert!(config.show_menu_on_right_click);
    }

    #[test]
    fn test_config_builder() {
        let config = TrayConfig::builder()
            .title("测试应用")
            .tooltip("这是一个测试应用")
            .icon_data("base64_icon_data".to_string())
            .icon_size(32, 32)
            .show_menu_on_left_click(true)
            .build()
            .unwrap();

        assert_eq!(config.title, "测试应用");
        assert_eq!(config.tooltip, Some("这是一个测试应用".to_string()));
        assert_eq!(config.icon_size, (32, 32));
        assert!(config.show_menu_on_left_click);
    }

    #[test]
    fn test_config_validation() {
        // 测试空标题
        let result = TrayConfig::builder()
            .title("")
            .icon_data("test".to_string())
            .build();
        assert!(result.is_err());

        // 测试缺少图标
        let result = TrayConfig::builder()
            .title("测试")
            .build();
        assert!(result.is_err());

        // 测试无效图标尺寸
        let result = TrayConfig::builder()
            .title("测试")
            .icon_data("test".to_string())
            .icon_size(0, 0)
            .build();
        assert!(result.is_err());
    }

    #[test]
    fn test_config_serialization() {
        let config = TrayConfig::builder()
            .title("测试应用")
            .tooltip("测试工具提示")
            .icon_data("test_icon_data".to_string())
            .build()
            .unwrap();

        let json = config.to_json().unwrap();
        let deserialized_config = TrayConfig::from_json(&json).unwrap();

        assert_eq!(config.title, deserialized_config.title);
        assert_eq!(config.tooltip, deserialized_config.tooltip);
        assert_eq!(config.icon_data, deserialized_config.icon_data);
    }

    #[test]
    fn test_should_show_menu_on_click() {
        let config = TrayConfig::builder()
            .title("测试")
            .icon_data("test".to_string())
            .show_menu_on_left_click(true)
            .show_menu_on_right_click(false)
            .build()
            .unwrap();

        assert!(config.should_show_menu_on_click(true));  // 左键
        assert!(!config.should_show_menu_on_click(false)); // 右键
    }
} 