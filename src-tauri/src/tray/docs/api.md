# 托盘模块 API 文档

本文档详细描述了托盘模块的所有公共 API。

## 核心类型

### TrayManager

托盘管理器是整个系统的核心协调器。

```rust
pub struct TrayManager {
    // 内部实现细节
}
```

#### 方法

##### `new(config: TrayConfig) -> TrayResult<Self>`

创建新的托盘管理器实例。

**参数:**
- `config`: 托盘配置

**返回值:**
- `TrayResult<Self>`: 成功时返回管理器实例，失败时返回错误

**示例:**
```rust
let config = TrayConfigBuilder::new().build();
let manager = TrayManager::new(config).await?;
```

##### `initialize() -> TrayResult<()>`

初始化托盘系统。

**返回值:**
- `TrayResult<()>`: 成功时返回 `()`，失败时返回错误

##### `show() -> TrayResult<()>`

显示托盘图标。

##### `hide() -> TrayResult<()>`

隐藏托盘图标。

##### `destroy() -> TrayResult<()>`

销毁托盘并清理所有资源。

##### `set_icon(icon: TrayIcon) -> TrayResult<()>`

设置托盘图标。

**参数:**
- `icon`: 要设置的图标

##### `set_menu(menu: TrayMenu) -> TrayResult<()>`

设置托盘菜单。

**参数:**
- `menu`: 要设置的菜单

##### `set_tooltip(tooltip: &str) -> TrayResult<()>`

设置托盘工具提示。

**参数:**
- `tooltip`: 工具提示文本

##### `add_event_handler(handler: Box<dyn TrayEventHandler>) -> TrayResult<()>`

添加事件处理器。

**参数:**
- `handler`: 事件处理器实例

##### `remove_event_handler(handler_id: &str) -> TrayResult<()>`

移除事件处理器。

**参数:**
- `handler_id`: 处理器 ID

##### `get_current_status() -> TrayStatus`

获取当前托盘状态。

**返回值:**
- `TrayStatus`: 当前状态

##### `is_running() -> bool`

检查托盘是否正在运行。

**返回值:**
- `bool`: 如果正在运行返回 `true`

##### `get_stats() -> TrayStats`

获取托盘统计信息。

**返回值:**
- `TrayStats`: 统计信息

##### `subscribe_state_changes() -> broadcast::Receiver<StateChangeEvent>`

订阅状态变更事件。

**返回值:**
- `broadcast::Receiver<StateChangeEvent>`: 状态变更事件接收器

### TrayManagerBuilder

托盘管理器构建器，提供流畅的 API 来创建托盘管理器。

```rust
pub struct TrayManagerBuilder {
    // 内部实现细节
}
```

#### 方法

##### `new() -> Self`

创建新的构建器实例。

##### `with_config(config: TrayConfig) -> Self`

设置配置。

##### `with_icon(icon: TrayIcon) -> Self`

设置图标。

##### `with_menu(menu: TrayMenu) -> Self`

设置菜单。

##### `add_event_handler(handler: Box<dyn TrayEventHandler>) -> Self`

添加事件处理器。

##### `build() -> TrayResult<TrayManager>`

构建托盘管理器。

## 配置类型

### TrayConfig

托盘配置结构体。

```rust
pub struct TrayConfig {
    pub title: String,
    pub tooltip: Option<String>,
    pub visible_on_startup: bool,
    pub icon_directory: Option<PathBuf>,
    pub platform_config: Option<PlatformConfig>,
}
```

### TrayConfigBuilder

配置构建器。

```rust
pub struct TrayConfigBuilder {
    // 内部实现细节
}
```

#### 方法

##### `new() -> Self`

创建新的配置构建器。

##### `title(title: &str) -> Self`

设置标题。

##### `tooltip(tooltip: &str) -> Self`

设置工具提示。

##### `visible_on_startup(visible: bool) -> Self`

设置启动时是否可见。

##### `icon_directory<P: AsRef<Path>>(path: P) -> Self`

设置图标目录。

##### `platform_config(config: PlatformConfig) -> Self`

设置平台特定配置。

##### `build() -> TrayConfig`

构建配置。

### PlatformConfig

平台特定配置。

```rust
pub struct PlatformConfig {
    pub windows: Option<WindowsConfig>,
    pub macos: Option<MacOSConfig>,
    pub linux: Option<LinuxConfig>,
}
```

## 图标类型

### TrayIcon

托盘图标。

```rust
pub struct TrayIcon {
    pub id: String,
    pub icon_type: TrayIconType,
    pub format: TrayIconFormat,
    pub data: Vec<u8>,
    pub size: Option<(u32, u32)>,
    pub created_at: SystemTime,
}
```

#### 方法

##### `from_file(id: &str, icon_type: TrayIconType, path: &str) -> TrayResult<Self>`

从文件创建图标。

##### `from_bytes(id: &str, icon_type: TrayIconType, format: TrayIconFormat, data: Vec<u8>) -> TrayResult<Self>`

从字节数据创建图标。

##### `validate() -> TrayResult<()>`

验证图标数据。

### TrayIconType

图标类型枚举。

```rust
pub enum TrayIconType {
    Default,
    Active,
    Inactive,
    Error,
    Warning,
    Success,
    Custom(String),
}
```

### TrayIconFormat

图标格式枚举。

```rust
pub enum TrayIconFormat {
    Png,
    Ico,
    Svg,
    Icns,
}
```

### TrayIconManager

图标管理器。

```rust
pub struct TrayIconManager {
    // 内部实现细节
}
```

#### 方法

##### `new(config: IconCacheConfig) -> Self`

创建图标管理器。

##### `add_icon(icon: TrayIcon) -> TrayResult<()>`

添加图标。

##### `remove_icon(icon_id: &str) -> TrayResult<()>`

移除图标。

##### `get_icon(icon_id: &str) -> Option<&TrayIcon>`

获取图标。

##### `set_current_icon(icon_id: &str) -> TrayResult<()>`

设置当前图标。

##### `get_current_icon() -> Option<&TrayIcon>`

获取当前图标。

##### `list_icons() -> Vec<String>`

列出所有图标 ID。

##### `clear_cache()`

清理缓存。

## 菜单类型

### TrayMenu

托盘菜单。

```rust
pub struct TrayMenu {
    pub items: Vec<TrayMenuItem>,
    pub created_at: SystemTime,
    pub updated_at: SystemTime,
}
```

#### 方法

##### `new() -> Self`

创建新菜单。

##### `add_item(item: TrayMenuItem)`

添加菜单项。

##### `add_separator()`

添加分隔符。

##### `remove_item(item_id: &str) -> bool`

移除菜单项。

##### `find_item(&self, item_id: &str) -> Option<&TrayMenuItem>`

查找菜单项。

##### `find_item_mut(&mut self, item_id: &str) -> Option<&mut TrayMenuItem>`

查找可变菜单项。

##### `validate() -> TrayResult<()>`

验证菜单。

### TrayMenuItem

托盘菜单项。

```rust
pub struct TrayMenuItem {
    pub id: String,
    pub label: String,
    pub item_type: TrayMenuItemType,
    pub enabled: bool,
    pub checked: bool,
    pub submenu: Option<Box<TrayMenu>>,
    pub icon: Option<String>,
    pub accelerator: Option<String>,
}
```

#### 方法

##### `new(id: &str, label: &str, item_type: TrayMenuItemType) -> Self`

创建新菜单项。

##### `enabled(mut self, enabled: bool) -> Self`

设置启用状态。

##### `checked(mut self, checked: bool) -> Self`

设置选中状态。

##### `with_submenu(mut self, submenu: TrayMenu) -> Self`

设置子菜单。

##### `with_icon(mut self, icon: &str) -> Self`

设置图标。

##### `with_accelerator(mut self, accelerator: &str) -> Self`

设置快捷键。

##### `set_enabled(&mut self, enabled: bool)`

设置启用状态。

##### `set_checked(&mut self, checked: bool)`

设置选中状态。

### TrayMenuItemType

菜单项类型枚举。

```rust
pub enum TrayMenuItemType {
    Normal,
    Separator,
    Checkbox,
    Radio,
    Submenu,
}
```

### TrayMenuBuilder

菜单构建器。

```rust
pub struct TrayMenuBuilder {
    // 内部实现细节
}
```

#### 方法

##### `new() -> Self`

创建新的菜单构建器。

##### `add_item(item: TrayMenuItem) -> Self`

添加菜单项。

##### `add_separator() -> Self`

添加分隔符。

##### `add_submenu(id: &str, label: &str, submenu: TrayMenu) -> Self`

添加子菜单。

##### `build() -> TrayMenu`

构建菜单。

## 事件类型

### TrayEvent

托盘事件。

```rust
pub struct TrayEvent {
    pub event_type: TrayEventType,
    pub timestamp: SystemTime,
    pub mouse_position: Option<(i32, i32)>,
    pub modifier_keys: ModifierKeys,
}
```

### TrayEventType

事件类型枚举。

```rust
pub enum TrayEventType {
    LeftClick,
    RightClick,
    DoubleClick,
    MiddleClick,
    MouseEnter,
    MouseLeave,
    MenuItemSelected { item_id: String },
    IconChanged { icon_id: String },
    StatusChanged { status: TrayStatus },
}
```

### TrayEventHandler

事件处理器 trait。

```rust
#[async_trait]
pub trait TrayEventHandler: Send + Sync {
    fn id(&self) -> &str;
    async fn handle_event(&mut self, event: TrayEvent) -> Result<(), Box<dyn std::error::Error + Send + Sync>>;
    fn priority(&self) -> u8 { 100 }
    fn can_handle(&self, event_type: &TrayEventType) -> bool { true }
}
```

### TrayEventDispatcher

事件分发器。

```rust
pub struct TrayEventDispatcher {
    // 内部实现细节
}
```

#### 方法

##### `new() -> Self`

创建事件分发器。

##### `add_handler(handler: Box<dyn TrayEventHandler>) -> TrayResult<()>`

添加处理器。

##### `remove_handler(handler_id: &str) -> TrayResult<()>`

移除处理器。

##### `dispatch_event(event: TrayEvent) -> TrayResult<()>`

分发事件。

##### `get_stats() -> EventStats`

获取事件统计。

## 状态类型

### TrayStatus

托盘状态枚举。

```rust
pub enum TrayStatus {
    Uninitialized,
    Initializing,
    Initialized,
    Visible,
    Hidden,
    Error(String),
    Destroyed,
}
```

### TrayState

状态管理器。

```rust
pub struct TrayState {
    // 内部实现细节
}
```

#### 方法

##### `new() -> Self`

创建状态管理器。

##### `get_current_status() -> TrayStatus`

获取当前状态。

##### `set_status(status: TrayStatus) -> TrayResult<()>`

设置状态。

##### `can_transition_to(target: &TrayStatus) -> bool`

检查是否可以转换到目标状态。

##### `get_history() -> Vec<StateHistoryEntry>`

获取状态历史。

##### `get_stats() -> StateStats`

获取状态统计。

##### `subscribe_changes() -> broadcast::Receiver<StateChangeEvent>`

订阅状态变更。

### StateChangeEvent

状态变更事件。

```rust
pub struct StateChangeEvent {
    pub previous_state: TrayStatus,
    pub current_state: TrayStatus,
    pub timestamp: SystemTime,
    pub trigger: String,
}
```

## 平台类型

### PlatformType

平台类型枚举。

```rust
pub enum PlatformType {
    Windows,
    MacOS,
    Linux,
    Unknown,
}
```

### PlatformTray

平台托盘 trait。

```rust
#[async_trait]
pub trait PlatformTray: Send + Sync {
    async fn initialize(&mut self) -> TrayResult<()>;
    async fn show(&mut self) -> TrayResult<()>;
    async fn hide(&mut self) -> TrayResult<()>;
    async fn destroy(&mut self) -> TrayResult<()>;
    async fn set_icon(&mut self, icon: &TrayIcon) -> TrayResult<()>;
    async fn set_menu(&mut self, menu: &TrayMenu) -> TrayResult<()>;
    async fn set_tooltip(&mut self, tooltip: &str) -> TrayResult<()>;
    fn is_supported() -> bool;
    fn get_platform_type() -> PlatformType;
}
```

### PlatformTrayFactory

平台托盘工厂。

```rust
pub struct PlatformTrayFactory;
```

#### 方法

##### `create_platform_tray(config: &TrayConfig) -> TrayResult<Box<dyn PlatformTray>>`

创建平台特定的托盘实现。

##### `detect_platform() -> PlatformType`

检测当前平台。

##### `is_supported() -> bool`

检查当前平台是否支持托盘。

## 错误类型

### TrayError

托盘错误枚举。

```rust
#[derive(Error, Debug)]
pub enum TrayError {
    #[error("托盘初始化失败: {reason}")]
    InitializationFailed { reason: String },
    
    #[error("图标加载失败: {path} - {reason}")]
    IconLoadFailed { path: String, reason: String },
    
    #[error("菜单创建失败: {reason}")]
    MenuCreationFailed { reason: String },
    
    #[error("事件处理失败: {event_type} - {reason}")]
    EventHandlingFailed { event_type: String, reason: String },
    
    #[error("无效状态: 当前状态 {current_state}，尝试转换到 {target_state}")]
    InvalidState { current_state: String, target_state: String },
    
    #[error("平台 {platform} 不支持操作: {operation}")]
    UnsupportedPlatform { platform: String, operation: String },
    
    #[error("配置错误: {field} - {reason}")]
    ConfigurationError { field: String, reason: String },
    
    #[error("菜单项数量超过限制: {count} > {max_items}")]
    MenuItemLimitExceeded { count: usize, max_items: usize },
    
    #[error("图标缓存已满: {current_size} bytes")]
    IconCacheFull { current_size: usize },
    
    #[error("处理器 {handler_id} 已存在")]
    HandlerAlreadyExists { handler_id: String },
    
    #[error("处理器 {handler_id} 未找到")]
    HandlerNotFound { handler_id: String },
    
    #[error("I/O 错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),
}
```

### TrayResult

托盘结果类型别名。

```rust
pub type TrayResult<T> = Result<T, TrayError>;
```

## 统计类型

### TrayStats

托盘统计信息。

```rust
pub struct TrayStats {
    pub uptime: Duration,
    pub total_events: u64,
    pub event_stats: EventStats,
    pub state_stats: StateStats,
    pub icon_stats: IconStats,
    pub memory_usage: MemoryUsage,
}
```

### EventStats

事件统计信息。

```rust
pub struct EventStats {
    pub total_events: u64,
    pub events_by_type: HashMap<String, u64>,
    pub average_processing_time: Duration,
    pub failed_events: u64,
}
```

### StateStats

状态统计信息。

```rust
pub struct StateStats {
    pub total_transitions: u64,
    pub time_in_each_state: HashMap<String, Duration>,
    pub current_state_duration: Duration,
}
```

### IconStats

图标统计信息。

```rust
pub struct IconStats {
    pub total_icons: usize,
    pub cache_size: usize,
    pub cache_hits: u64,
    pub cache_misses: u64,
}
```

### MemoryUsage

内存使用统计。

```rust
pub struct MemoryUsage {
    pub total_allocated: usize,
    pub icon_cache_size: usize,
    pub event_queue_size: usize,
}
```

## 常量

```rust
/// 托盘模块版本
pub const TRAY_MODULE_VERSION: &str = "1.0.0";

/// 默认托盘图标大小
pub const DEFAULT_TRAY_ICON_SIZE: (u32, u32) = (16, 16);

/// 最大菜单项数量
pub const MAX_MENU_ITEMS: usize = 50;
```

## 使用示例

### 基本用法

```rust
use secure_password_lib::tray::*;

#[tokio::main]
async fn main() -> TrayResult<()> {
    let config = TrayConfigBuilder::new()
        .title("我的应用")
        .tooltip("应用正在运行")
        .build();
    
    let manager = TrayManager::new(config).await?;
    manager.initialize().await?;
    manager.show().await?;
    
    // 应用逻辑...
    
    manager.destroy().await?;
    Ok(())
}
```

### 使用构建器

```rust
let manager = TrayManagerBuilder::new()
    .with_config(config)
    .with_icon(icon)
    .with_menu(menu)
    .add_event_handler(handler)
    .build()
    .await?;
```

这个 API 文档提供了托盘模块所有公共接口的详细说明，包括参数、返回值和使用示例。 