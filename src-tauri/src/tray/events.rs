/// 托盘事件处理模块
/// 
/// 定义托盘事件类型、事件处理器和事件分发机制

use crate::tray::{TrayError, TrayResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 托盘事件类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TrayEventType {
    /// 左键单击
    LeftClick,
    /// 右键单击
    RightClick,
    /// 中键单击
    MiddleClick,
    /// 双击（通常是左键）
    DoubleClick,
    /// 鼠标进入托盘图标区域
    MouseEnter,
    /// 鼠标离开托盘图标区域
    MouseLeave,
    /// 鼠标在托盘图标上移动
    MouseMove,
    /// 菜单项被选择
    MenuItemSelected,
    /// 托盘图标被创建
    TrayCreated,
    /// 托盘图标被销毁
    TrayDestroyed,
    /// 托盘图标更新
    TrayUpdated,
    /// 系统主题变化（如深色/浅色模式切换）
    SystemThemeChanged,
    /// 自定义事件
    Custom(String),
}

impl std::fmt::Display for TrayEventType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::LeftClick => write!(f, "左键单击"),
            Self::RightClick => write!(f, "右键单击"),
            Self::MiddleClick => write!(f, "中键单击"),
            Self::DoubleClick => write!(f, "双击"),
            Self::MouseEnter => write!(f, "鼠标进入"),
            Self::MouseLeave => write!(f, "鼠标离开"),
            Self::MouseMove => write!(f, "鼠标移动"),
            Self::MenuItemSelected => write!(f, "菜单项选择"),
            Self::TrayCreated => write!(f, "托盘创建"),
            Self::TrayDestroyed => write!(f, "托盘销毁"),
            Self::TrayUpdated => write!(f, "托盘更新"),
            Self::SystemThemeChanged => write!(f, "系统主题变化"),
            Self::Custom(name) => write!(f, "自定义事件: {}", name),
        }
    }
}

/// 托盘事件数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayEvent {
    /// 事件类型
    pub event_type: TrayEventType,
    
    /// 事件发生的时间戳（毫秒）
    pub timestamp: u64,
    
    /// 鼠标位置（相对于屏幕）
    pub mouse_position: Option<(i32, i32)>,
    
    /// 托盘图标位置（相对于屏幕）
    pub tray_position: Option<(i32, i32)>,
    
    /// 菜单项ID（仅在菜单项选择事件中有效）
    pub menu_item_id: Option<String>,
    
    /// 鼠标按钮状态
    pub button_state: MouseButtonState,
    
    /// 键盘修饰键状态
    pub modifier_keys: ModifierKeys,
    
    /// 事件的附加数据
    pub data: HashMap<String, serde_json::Value>,
}

/// 鼠标按钮状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum MouseButtonState {
    /// 按钮按下
    Pressed,
    /// 按钮释放
    Released,
    /// 按钮未按下
    None,
}

/// 键盘修饰键状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub struct ModifierKeys {
    /// Ctrl 键是否按下
    pub ctrl: bool,
    /// Alt 键是否按下
    pub alt: bool,
    /// Shift 键是否按下
    pub shift: bool,
    /// Meta 键（Windows 键或 Cmd 键）是否按下
    pub meta: bool,
}

impl Default for ModifierKeys {
    fn default() -> Self {
        Self {
            ctrl: false,
            alt: false,
            shift: false,
            meta: false,
        }
    }
}

impl TrayEvent {
    /// 创建新的托盘事件
    pub fn new(event_type: TrayEventType) -> Self {
        Self {
            event_type,
            timestamp: current_timestamp_millis(),
            mouse_position: None,
            tray_position: None,
            menu_item_id: None,
            button_state: MouseButtonState::None,
            modifier_keys: ModifierKeys::default(),
            data: HashMap::new(),
        }
    }

    /// 创建鼠标点击事件
    pub fn mouse_click(
        event_type: TrayEventType,
        mouse_position: (i32, i32),
        button_state: MouseButtonState,
        modifier_keys: ModifierKeys,
    ) -> Self {
        Self {
            event_type,
            timestamp: current_timestamp_millis(),
            mouse_position: Some(mouse_position),
            tray_position: None,
            menu_item_id: None,
            button_state,
            modifier_keys,
            data: HashMap::new(),
        }
    }

    /// 创建菜单项选择事件
    pub fn menu_item_selected(menu_item_id: String) -> Self {
        Self {
            event_type: TrayEventType::MenuItemSelected,
            timestamp: current_timestamp_millis(),
            mouse_position: None,
            tray_position: None,
            menu_item_id: Some(menu_item_id),
            button_state: MouseButtonState::None,
            modifier_keys: ModifierKeys::default(),
            data: HashMap::new(),
        }
    }

    /// 设置鼠标位置
    pub fn with_mouse_position(mut self, position: (i32, i32)) -> Self {
        self.mouse_position = Some(position);
        self
    }

    /// 设置托盘位置
    pub fn with_tray_position(mut self, position: (i32, i32)) -> Self {
        self.tray_position = Some(position);
        self
    }

    /// 添加附加数据
    pub fn with_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.data.insert(key, value);
        self
    }

    /// 获取附加数据
    pub fn get_data(&self, key: &str) -> Option<&serde_json::Value> {
        self.data.get(key)
    }

    /// 检查是否为鼠标事件
    pub fn is_mouse_event(&self) -> bool {
        matches!(
            self.event_type,
            TrayEventType::LeftClick
                | TrayEventType::RightClick
                | TrayEventType::MiddleClick
                | TrayEventType::DoubleClick
                | TrayEventType::MouseEnter
                | TrayEventType::MouseLeave
                | TrayEventType::MouseMove
        )
    }

    /// 检查是否为点击事件
    pub fn is_click_event(&self) -> bool {
        matches!(
            self.event_type,
            TrayEventType::LeftClick
                | TrayEventType::RightClick
                | TrayEventType::MiddleClick
                | TrayEventType::DoubleClick
        )
    }
}

/// 托盘事件处理器特征
/// 
/// 实现此特征以处理托盘事件
#[async_trait::async_trait]
pub trait TrayEventHandler: Send + Sync {
    /// 处理托盘事件
    /// 
    /// # 参数
    /// 
    /// * `event` - 要处理的托盘事件
    /// 
    /// # 返回值
    /// 
    /// 返回处理结果，如果返回错误，事件处理将被中断
    async fn handle_event(&self, event: &TrayEvent) -> TrayResult<()>;

    /// 获取处理器名称
    fn name(&self) -> &str;

    /// 检查是否应该处理指定类型的事件
    fn should_handle(&self, event_type: &TrayEventType) -> bool {
        // 默认处理所有事件
        true
    }

    /// 获取处理器优先级（数值越小优先级越高）
    fn priority(&self) -> u32 {
        100
    }
}

/// 事件处理器包装器
#[derive(Clone)]
struct EventHandlerWrapper {
    handler: Arc<dyn TrayEventHandler>,
    priority: u32,
}

/// 托盘事件分发器
/// 
/// 负责管理事件处理器和分发事件
pub struct TrayEventDispatcher {
    /// 事件处理器列表
    handlers: Arc<RwLock<Vec<EventHandlerWrapper>>>,
    
    /// 事件统计信息
    event_stats: Arc<RwLock<EventStatistics>>,
}

/// 事件统计信息
#[derive(Debug, Default)]
pub struct EventStatistics {
    /// 总事件数量
    pub total_events: u64,
    
    /// 成功处理的事件数量
    pub successful_events: u64,
    
    /// 失败的事件数量
    pub failed_events: u64,
    
    /// 各类型事件的数量
    pub events_by_type: HashMap<TrayEventType, u64>,
    
    /// 平均处理时间（毫秒）
    pub average_processing_time_ms: f64,
}

impl TrayEventDispatcher {
    /// 创建新的事件分发器
    pub fn new() -> Self {
        Self {
            handlers: Arc::new(RwLock::new(Vec::new())),
            event_stats: Arc::new(RwLock::new(EventStatistics::default())),
        }
    }

    /// 注册事件处理器
    pub async fn register_handler(&self, handler: Arc<dyn TrayEventHandler>) -> TrayResult<()> {
        let priority = handler.priority();
        let wrapper = EventHandlerWrapper { handler, priority };
        
        let mut handlers = self.handlers.write().await;
        handlers.push(wrapper);
        
        // 按优先级排序（优先级数值越小越靠前）
        handlers.sort_by_key(|h| h.priority);
        
        Ok(())
    }

    /// 注销事件处理器
    pub async fn unregister_handler(&self, handler_name: &str) -> TrayResult<bool> {
        let mut handlers = self.handlers.write().await;
        let initial_len = handlers.len();
        
        handlers.retain(|wrapper| wrapper.handler.name() != handler_name);
        
        Ok(handlers.len() < initial_len)
    }

    /// 分发事件到所有注册的处理器
    pub async fn dispatch_event(&self, event: TrayEvent) -> TrayResult<()> {
        let start_time = std::time::Instant::now();
        let handlers = self.handlers.read().await;
        
        let mut successful_handlers = 0;
        let mut failed_handlers = 0;
        
        // 按优先级顺序处理事件
        for wrapper in handlers.iter() {
            if !wrapper.handler.should_handle(&event.event_type) {
                continue;
            }
            
            match wrapper.handler.handle_event(&event).await {
                Ok(()) => {
                    successful_handlers += 1;
                    log::debug!(
                        "事件处理器 '{}' 成功处理事件: {}",
                        wrapper.handler.name(),
                        event.event_type
                    );
                }
                Err(e) => {
                    failed_handlers += 1;
                    log::error!(
                        "事件处理器 '{}' 处理事件失败: {} - {}",
                        wrapper.handler.name(),
                        event.event_type,
                        e
                    );
                    
                    // 如果是严重错误，停止后续处理
                    if !e.is_recoverable() {
                        return Err(e);
                    }
                }
            }
        }
        
        // 更新统计信息
        let processing_time = start_time.elapsed().as_millis() as f64;
        self.update_statistics(&event, successful_handlers > 0, processing_time).await;
        
        if successful_handlers == 0 && failed_handlers > 0 {
            return Err(TrayError::event_handling_failed(
                event.event_type.to_string(),
                "所有事件处理器都失败了",
            ));
        }
        
        Ok(())
    }

    /// 获取事件统计信息
    pub async fn get_statistics(&self) -> EventStatistics {
        self.event_stats.read().await.clone()
    }

    /// 重置事件统计信息
    pub async fn reset_statistics(&self) {
        let mut stats = self.event_stats.write().await;
        *stats = EventStatistics::default();
    }

    /// 获取已注册的处理器数量
    pub async fn handler_count(&self) -> usize {
        self.handlers.read().await.len()
    }

    /// 获取已注册的处理器名称列表
    pub async fn get_handler_names(&self) -> Vec<String> {
        let handlers = self.handlers.read().await;
        handlers
            .iter()
            .map(|wrapper| wrapper.handler.name().to_string())
            .collect()
    }

    /// 更新事件统计信息
    async fn update_statistics(
        &self,
        event: &TrayEvent,
        success: bool,
        processing_time_ms: f64,
    ) {
        let mut stats = self.event_stats.write().await;
        
        stats.total_events += 1;
        
        if success {
            stats.successful_events += 1;
        } else {
            stats.failed_events += 1;
        }
        
        // 更新事件类型统计
        *stats.events_by_type.entry(event.event_type.clone()).or_insert(0) += 1;
        
        // 更新平均处理时间
        let total_time = stats.average_processing_time_ms * (stats.total_events - 1) as f64;
        stats.average_processing_time_ms = (total_time + processing_time_ms) / stats.total_events as f64;
    }
}

impl Default for TrayEventDispatcher {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for EventStatistics {
    fn clone(&self) -> Self {
        Self {
            total_events: self.total_events,
            successful_events: self.successful_events,
            failed_events: self.failed_events,
            events_by_type: self.events_by_type.clone(),
            average_processing_time_ms: self.average_processing_time_ms,
        }
    }
}

/// 获取当前时间戳（毫秒）
fn current_timestamp_millis() -> u64 {
    std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_millis() as u64
}

/// 简单的事件处理器实现示例
pub struct SimpleEventHandler {
    name: String,
    handled_events: Vec<TrayEventType>,
    priority: u32,
}

impl SimpleEventHandler {
    /// 创建新的简单事件处理器
    pub fn new(
        name: impl Into<String>,
        handled_events: Vec<TrayEventType>,
        priority: u32,
    ) -> Self {
        Self {
            name: name.into(),
            handled_events,
            priority,
        }
    }
}

#[async_trait::async_trait]
impl TrayEventHandler for SimpleEventHandler {
    async fn handle_event(&self, event: &TrayEvent) -> TrayResult<()> {
        log::info!(
            "处理器 '{}' 处理事件: {} (时间戳: {})",
            self.name,
            event.event_type,
            event.timestamp
        );
        Ok(())
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn should_handle(&self, event_type: &TrayEventType) -> bool {
        self.handled_events.contains(event_type)
    }

    fn priority(&self) -> u32 {
        self.priority
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::{AtomicU32, Ordering};

    struct TestEventHandler {
        name: String,
        call_count: Arc<AtomicU32>,
        should_fail: bool,
    }

    impl TestEventHandler {
        fn new(name: &str, should_fail: bool) -> Self {
            Self {
                name: name.to_string(),
                call_count: Arc::new(AtomicU32::new(0)),
                should_fail,
            }
        }

        fn get_call_count(&self) -> u32 {
            self.call_count.load(Ordering::Relaxed)
        }
    }

    #[async_trait::async_trait]
    impl TrayEventHandler for TestEventHandler {
        async fn handle_event(&self, _event: &TrayEvent) -> TrayResult<()> {
            self.call_count.fetch_add(1, Ordering::Relaxed);
            
            if self.should_fail {
                Err(TrayError::event_handling_failed(
                    "test",
                    "测试失败",
                ))
            } else {
                Ok(())
            }
        }

        fn name(&self) -> &str {
            &self.name
        }
    }

    #[tokio::test]
    async fn test_event_creation() {
        let event = TrayEvent::new(TrayEventType::LeftClick);
        assert_eq!(event.event_type, TrayEventType::LeftClick);
        assert!(event.timestamp > 0);
    }

    #[tokio::test]
    async fn test_mouse_click_event() {
        let event = TrayEvent::mouse_click(
            TrayEventType::LeftClick,
            (100, 200),
            MouseButtonState::Pressed,
            ModifierKeys { ctrl: true, ..Default::default() },
        );
        
        assert_eq!(event.event_type, TrayEventType::LeftClick);
        assert_eq!(event.mouse_position, Some((100, 200)));
        assert_eq!(event.button_state, MouseButtonState::Pressed);
        assert!(event.modifier_keys.ctrl);
        assert!(event.is_mouse_event());
        assert!(event.is_click_event());
    }

    #[tokio::test]
    async fn test_event_dispatcher() {
        let dispatcher = TrayEventDispatcher::new();
        
        let handler1 = Arc::new(TestEventHandler::new("handler1", false));
        let handler2 = Arc::new(TestEventHandler::new("handler2", false));
        
        dispatcher.register_handler(handler1.clone()).await.unwrap();
        dispatcher.register_handler(handler2.clone()).await.unwrap();
        
        assert_eq!(dispatcher.handler_count().await, 2);
        
        let event = TrayEvent::new(TrayEventType::LeftClick);
        dispatcher.dispatch_event(event).await.unwrap();
        
        assert_eq!(handler1.get_call_count(), 1);
        assert_eq!(handler2.get_call_count(), 1);
        
        let stats = dispatcher.get_statistics().await;
        assert_eq!(stats.total_events, 1);
        assert_eq!(stats.successful_events, 1);
        assert_eq!(stats.failed_events, 0);
    }

    #[tokio::test]
    async fn test_event_handler_failure() {
        let dispatcher = TrayEventDispatcher::new();
        
        let failing_handler = Arc::new(TestEventHandler::new("failing", true));
        dispatcher.register_handler(failing_handler.clone()).await.unwrap();
        
        let event = TrayEvent::new(TrayEventType::LeftClick);
        let result = dispatcher.dispatch_event(event).await;
        
        assert!(result.is_err());
        assert_eq!(failing_handler.get_call_count(), 1);
        
        let stats = dispatcher.get_statistics().await;
        assert_eq!(stats.total_events, 1);
        assert_eq!(stats.successful_events, 0);
        assert_eq!(stats.failed_events, 1);
    }

    #[tokio::test]
    async fn test_handler_unregistration() {
        let dispatcher = TrayEventDispatcher::new();
        
        let handler = Arc::new(TestEventHandler::new("test_handler", false));
        dispatcher.register_handler(handler).await.unwrap();
        
        assert_eq!(dispatcher.handler_count().await, 1);
        
        let removed = dispatcher.unregister_handler("test_handler").await.unwrap();
        assert!(removed);
        assert_eq!(dispatcher.handler_count().await, 0);
        
        let not_removed = dispatcher.unregister_handler("nonexistent").await.unwrap();
        assert!(!not_removed);
    }

    #[test]
    fn test_modifier_keys() {
        let modifiers = ModifierKeys {
            ctrl: true,
            alt: false,
            shift: true,
            meta: false,
        };
        
        assert!(modifiers.ctrl);
        assert!(!modifiers.alt);
        assert!(modifiers.shift);
        assert!(!modifiers.meta);
    }

    #[test]
    fn test_event_type_display() {
        assert_eq!(TrayEventType::LeftClick.to_string(), "左键单击");
        assert_eq!(TrayEventType::Custom("test".to_string()).to_string(), "自定义事件: test");
    }
} 