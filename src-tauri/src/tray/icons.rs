/// 托盘图标管理模块
/// 
/// 提供跨平台的图标加载、缓存和管理功能，支持：
/// - 多种图标格式（PNG、ICO、SVG等）
/// - 图标缓存机制
/// - 动态图标切换
/// - 平台特定的图标优化

use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::{Arc, RwLock};
use serde::{Deserialize, Serialize};
use crate::tray::errors::{TrayError, TrayResult};

/// 支持的图标格式
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TrayIconFormat {
    /// PNG 格式
    Png,
    /// ICO 格式（Windows 推荐）
    Ico,
    /// SVG 格式
    Svg,
    /// ICNS 格式（macOS 推荐）
    Icns,
    /// 原始字节数据
    Raw,
}

impl TrayIconFormat {
    /// 从文件扩展名推断图标格式
    pub fn from_extension(ext: &str) -> Option<Self> {
        match ext.to_lowercase().as_str() {
            "png" => Some(Self::Png),
            "ico" => Some(Self::Ico),
            "svg" => Some(Self::Svg),
            "icns" => Some(Self::Icns),
            _ => None,
        }
    }

    /// 获取支持的格式列表
    pub fn supported_formats() -> Vec<String> {
        vec![
            "png".to_string(),
            "ico".to_string(),
            "svg".to_string(),
            "icns".to_string(),
        ]
    }

    /// 检查格式是否为矢量格式
    pub fn is_vector(&self) -> bool {
        matches!(self, Self::Svg)
    }

    /// 获取推荐的平台格式
    #[cfg(target_os = "windows")]
    pub fn platform_preferred() -> Self {
        Self::Ico
    }

    #[cfg(target_os = "macos")]
    pub fn platform_preferred() -> Self {
        Self::Icns
    }

    #[cfg(target_os = "linux")]
    pub fn platform_preferred() -> Self {
        Self::Png
    }
}

/// 托盘图标类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TrayIconType {
    /// 默认图标
    Default,
    /// 活动状态图标
    Active,
    /// 非活动状态图标
    Inactive,
    /// 错误状态图标
    Error,
    /// 警告状态图标
    Warning,
    /// 通知状态图标
    Notification,
    /// 自定义图标
    Custom(String),
}

impl TrayIconType {
    /// 获取图标类型的字符串表示
    pub fn as_str(&self) -> &str {
        match self {
            Self::Default => "default",
            Self::Active => "active",
            Self::Inactive => "inactive",
            Self::Error => "error",
            Self::Warning => "warning",
            Self::Notification => "notification",
            Self::Custom(name) => name,
        }
    }
}

/// 托盘图标数据
#[derive(Debug, Clone)]
pub struct TrayIcon {
    /// 图标ID
    pub id: String,
    /// 图标类型
    pub icon_type: TrayIconType,
    /// 图标格式
    pub format: TrayIconFormat,
    /// 图标数据
    pub data: Vec<u8>,
    /// 图标尺寸
    pub size: (u32, u32),
    /// 图标路径（如果从文件加载）
    pub path: Option<PathBuf>,
    /// 创建时间戳
    pub created_at: std::time::SystemTime,
}

impl TrayIcon {
    /// 创建新的托盘图标
    pub fn new(
        id: impl Into<String>,
        icon_type: TrayIconType,
        format: TrayIconFormat,
        data: Vec<u8>,
        size: (u32, u32),
    ) -> Self {
        Self {
            id: id.into(),
            icon_type,
            format,
            data,
            size,
            path: None,
            created_at: std::time::SystemTime::now(),
        }
    }

    /// 从文件加载图标
    pub async fn from_file(
        id: impl Into<String>,
        icon_type: TrayIconType,
        path: impl AsRef<Path>,
    ) -> TrayResult<Self> {
        let path = path.as_ref();
        let data = tokio::fs::read(path).await.map_err(|e| {
            TrayError::icon_load_failed(
                path.display().to_string(),
                format!("读取文件失败: {}", e),
            )
        })?;

        let format = path
            .extension()
            .and_then(|ext| ext.to_str())
            .and_then(TrayIconFormat::from_extension)
            .ok_or_else(|| {
                TrayError::UnsupportedIconFormat {
                    format: path.extension()
                        .and_then(|ext| ext.to_str())
                        .unwrap_or("unknown")
                        .to_string(),
                    supported_formats: TrayIconFormat::supported_formats(),
                }
            })?;

        // 简单的尺寸检测（实际应用中可能需要更复杂的图像解析）
        let size = Self::detect_image_size(&data, &format)?;

        Ok(Self {
            id: id.into(),
            icon_type,
            format,
            data,
            size,
            path: Some(path.to_path_buf()),
            created_at: std::time::SystemTime::now(),
        })
    }

    /// 从字节数据创建图标
    pub fn from_bytes(
        id: impl Into<String>,
        icon_type: TrayIconType,
        format: TrayIconFormat,
        data: Vec<u8>,
    ) -> TrayResult<Self> {
        let size = Self::detect_image_size(&data, &format)?;
        Ok(Self::new(id, icon_type, format, data, size))
    }

    /// 检测图像尺寸（简化实现）
    fn detect_image_size(data: &[u8], format: &TrayIconFormat) -> TrayResult<(u32, u32)> {
        match format {
            TrayIconFormat::Png => {
                // PNG 文件头检查
                if data.len() >= 24 && &data[0..8] == b"\x89PNG\r\n\x1a\n" {
                    let width = u32::from_be_bytes([data[16], data[17], data[18], data[19]]);
                    let height = u32::from_be_bytes([data[20], data[21], data[22], data[23]]);
                    Ok((width, height))
                } else {
                    Err(TrayError::icon_load_failed(
                        "unknown",
                        "无效的PNG文件格式",
                    ))
                }
            }
            TrayIconFormat::Ico => {
                // ICO 文件头检查
                if data.len() >= 6 && &data[0..4] == b"\x00\x00\x01\x00" {
                    // 简化处理，返回默认尺寸
                    Ok((16, 16))
                } else {
                    Err(TrayError::icon_load_failed(
                        "unknown",
                        "无效的ICO文件格式",
                    ))
                }
            }
            _ => {
                // 其他格式返回默认尺寸
                Ok((16, 16))
            }
        }
    }

    /// 验证图标数据
    pub fn validate(&self) -> TrayResult<()> {
        if self.data.is_empty() {
            return Err(TrayError::icon_load_failed(
                &self.id,
                "图标数据为空",
            ));
        }

        if self.size.0 == 0 || self.size.1 == 0 {
            return Err(TrayError::InvalidIconSize {
                width: self.size.0,
                height: self.size.1,
                expected_width: 16,
                expected_height: 16,
            });
        }

        Ok(())
    }

    /// 获取图标的内存占用大小
    pub fn memory_size(&self) -> usize {
        self.data.len()
    }
}

/// 图标缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IconCacheConfig {
    /// 最大缓存大小（字节）
    pub max_cache_size: usize,
    /// 最大缓存项数量
    pub max_cache_items: usize,
    /// 缓存过期时间（秒）
    pub cache_ttl: u64,
    /// 是否启用预加载
    pub enable_preload: bool,
}

impl Default for IconCacheConfig {
    fn default() -> Self {
        Self {
            max_cache_size: 10 * 1024 * 1024, // 10MB
            max_cache_items: 100,
            cache_ttl: 3600, // 1小时
            enable_preload: true,
        }
    }
}

/// 托盘图标管理器
#[derive(Debug)]
pub struct TrayIconManager {
    /// 图标缓存
    cache: Arc<RwLock<HashMap<String, TrayIcon>>>,
    /// 当前活动图标
    current_icon: Arc<RwLock<Option<String>>>,
    /// 缓存配置
    config: IconCacheConfig,
    /// 缓存统计
    cache_stats: Arc<RwLock<CacheStats>>,
}

/// 缓存统计信息
#[derive(Debug, Default, Clone)]
pub struct CacheStats {
    /// 缓存命中次数
    hits: u64,
    /// 缓存未命中次数
    misses: u64,
    /// 当前缓存大小
    current_size: usize,
    /// 当前缓存项数量
    current_items: usize,
}

impl TrayIconManager {
    /// 创建新的图标管理器
    pub fn new(config: IconCacheConfig) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            current_icon: Arc::new(RwLock::new(None)),
            config,
            cache_stats: Arc::new(RwLock::new(CacheStats::default())),
        }
    }

    /// 使用默认配置创建图标管理器
    pub fn with_default_config() -> Self {
        Self::new(IconCacheConfig::default())
    }

    /// 添加图标到缓存
    pub async fn add_icon(&self, icon: TrayIcon) -> TrayResult<()> {
        icon.validate()?;

        let mut cache = self.cache.write().unwrap();
        let mut stats = self.cache_stats.write().unwrap();

        // 检查缓存限制
        if cache.len() >= self.config.max_cache_items {
            self.evict_oldest(&mut cache, &mut stats);
        }

        let icon_size = icon.memory_size();
        if stats.current_size + icon_size > self.config.max_cache_size {
            self.evict_by_size(&mut cache, &mut stats, icon_size);
        }

        let icon_id = icon.id.clone();
        cache.insert(icon_id, icon);
        stats.current_items = cache.len();
        stats.current_size += icon_size;

        Ok(())
    }

    /// 从文件加载并添加图标
    pub async fn load_icon_from_file(
        &self,
        id: impl Into<String>,
        icon_type: TrayIconType,
        path: impl AsRef<Path>,
    ) -> TrayResult<()> {
        let icon = TrayIcon::from_file(id, icon_type, path).await?;
        self.add_icon(icon).await
    }

    /// 获取图标
    pub fn get_icon(&self, id: &str) -> Option<TrayIcon> {
        let cache = self.cache.read().unwrap();
        let mut stats = self.cache_stats.write().unwrap();

        if let Some(icon) = cache.get(id) {
            stats.hits += 1;
            Some(icon.clone())
        } else {
            stats.misses += 1;
            None
        }
    }

    /// 设置当前活动图标
    pub fn set_current_icon(&self, icon_id: impl Into<String>) -> TrayResult<()> {
        let icon_id = icon_id.into();
        
        // 检查图标是否存在
        if !self.cache.read().unwrap().contains_key(&icon_id) {
            return Err(TrayError::MenuItemNotFound { id: icon_id });
        }

        *self.current_icon.write().unwrap() = Some(icon_id);
        Ok(())
    }

    /// 获取当前活动图标
    pub fn get_current_icon(&self) -> Option<TrayIcon> {
        let current_id = self.current_icon.read().unwrap();
        if let Some(ref id) = *current_id {
            self.get_icon(id)
        } else {
            None
        }
    }

    /// 移除图标
    pub fn remove_icon(&self, id: &str) -> bool {
        let mut cache = self.cache.write().unwrap();
        let mut stats = self.cache_stats.write().unwrap();

        if let Some(icon) = cache.remove(id) {
            stats.current_items = cache.len();
            stats.current_size = stats.current_size.saturating_sub(icon.memory_size());
            
            // 如果移除的是当前图标，清除当前图标引用
            let mut current = self.current_icon.write().unwrap();
            if current.as_ref() == Some(&id.to_string()) {
                *current = None;
            }
            
            true
        } else {
            false
        }
    }

    /// 清空缓存
    pub fn clear_cache(&self) {
        let mut cache = self.cache.write().unwrap();
        let mut stats = self.cache_stats.write().unwrap();
        
        cache.clear();
        stats.current_items = 0;
        stats.current_size = 0;
        
        *self.current_icon.write().unwrap() = None;
    }

    /// 获取缓存统计信息
    pub fn get_cache_stats(&self) -> CacheStats {
        self.cache_stats.read().unwrap().clone()
    }

    /// 获取所有图标ID列表
    pub fn list_icons(&self) -> Vec<String> {
        self.cache.read().unwrap().keys().cloned().collect()
    }

    /// 按类型获取图标列表
    pub fn list_icons_by_type(&self, icon_type: &TrayIconType) -> Vec<String> {
        self.cache
            .read()
            .unwrap()
            .iter()
            .filter(|(_, icon)| &icon.icon_type == icon_type)
            .map(|(id, _)| id.clone())
            .collect()
    }

    /// 预加载默认图标集
    pub async fn preload_default_icons(&self, icon_dir: impl AsRef<Path>) -> TrayResult<()> {
        if !self.config.enable_preload {
            return Ok(());
        }

        let icon_dir = icon_dir.as_ref();
        let default_icons = [
            ("default", TrayIconType::Default),
            ("active", TrayIconType::Active),
            ("inactive", TrayIconType::Inactive),
            ("error", TrayIconType::Error),
            ("warning", TrayIconType::Warning),
            ("notification", TrayIconType::Notification),
        ];

        for (name, icon_type) in &default_icons {
            let icon_path = icon_dir.join(format!("{}.png", name));
            if icon_path.exists() {
                if let Err(e) = self.load_icon_from_file(*name, icon_type.clone(), icon_path).await {
                    // 预加载失败不应该阻止程序运行，只记录错误
                    eprintln!("预加载图标失败 {}: {}", name, e);
                }
            }
        }

        Ok(())
    }

    /// 驱逐最旧的图标
    fn evict_oldest(&self, cache: &mut HashMap<String, TrayIcon>, stats: &mut CacheStats) {
        if let Some((oldest_id, _)) = cache
            .iter()
            .min_by_key(|(_, icon)| icon.created_at)
            .map(|(id, icon)| (id.clone(), icon.memory_size()))
        {
            if let Some(icon) = cache.remove(&oldest_id) {
                stats.current_size = stats.current_size.saturating_sub(icon.memory_size());
            }
        }
    }

    /// 按大小驱逐图标
    fn evict_by_size(
        &self,
        cache: &mut HashMap<String, TrayIcon>,
        stats: &mut CacheStats,
        needed_size: usize,
    ) {
        let mut freed_size = 0;
        let mut to_remove = Vec::new();

        // 按创建时间排序，优先移除最旧的
        let mut icons: Vec<_> = cache.iter().collect();
        icons.sort_by_key(|(_, icon)| icon.created_at);

        for (id, icon) in icons {
            if freed_size >= needed_size {
                break;
            }
            to_remove.push(id.clone());
            freed_size += icon.memory_size();
        }

        for id in to_remove {
            if let Some(icon) = cache.remove(&id) {
                stats.current_size = stats.current_size.saturating_sub(icon.memory_size());
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[test]
    fn test_icon_format_detection() {
        assert_eq!(TrayIconFormat::from_extension("png"), Some(TrayIconFormat::Png));
        assert_eq!(TrayIconFormat::from_extension("ico"), Some(TrayIconFormat::Ico));
        assert_eq!(TrayIconFormat::from_extension("svg"), Some(TrayIconFormat::Svg));
        assert_eq!(TrayIconFormat::from_extension("unknown"), None);
    }

    #[test]
    fn test_icon_type_string_conversion() {
        assert_eq!(TrayIconType::Default.as_str(), "default");
        assert_eq!(TrayIconType::Active.as_str(), "active");
        assert_eq!(TrayIconType::Custom("test".to_string()).as_str(), "test");
    }

    #[test]
    fn test_icon_creation() {
        let icon = TrayIcon::new(
            "test",
            TrayIconType::Default,
            TrayIconFormat::Png,
            vec![1, 2, 3, 4],
            (16, 16),
        );

        assert_eq!(icon.id, "test");
        assert_eq!(icon.icon_type, TrayIconType::Default);
        assert_eq!(icon.format, TrayIconFormat::Png);
        assert_eq!(icon.data, vec![1, 2, 3, 4]);
        assert_eq!(icon.size, (16, 16));
    }

    #[tokio::test]
    async fn test_icon_manager_basic_operations() {
        let manager = TrayIconManager::with_default_config();

        // 创建测试图标
        let icon = TrayIcon::new(
            "test",
            TrayIconType::Default,
            TrayIconFormat::Png,
            vec![1, 2, 3, 4],
            (16, 16),
        );

        // 添加图标
        manager.add_icon(icon).await.unwrap();

        // 获取图标
        let retrieved = manager.get_icon("test");
        assert!(retrieved.is_some());

        // 设置当前图标
        manager.set_current_icon("test").unwrap();
        let current = manager.get_current_icon();
        assert!(current.is_some());

        // 移除图标
        assert!(manager.remove_icon("test"));
        assert!(manager.get_icon("test").is_none());
    }

    #[tokio::test]
    async fn test_cache_limits() {
        let config = IconCacheConfig {
            max_cache_items: 2,
            max_cache_size: 100,
            ..Default::default()
        };
        let manager = TrayIconManager::new(config);

        // 添加超过限制的图标
        for i in 0..3 {
            let icon = TrayIcon::new(
                format!("test{}", i),
                TrayIconType::Default,
                TrayIconFormat::Png,
                vec![0; 50], // 50字节
                (16, 16),
            );
            manager.add_icon(icon).await.unwrap();
        }

        // 检查缓存项数量限制
        let stats = manager.get_cache_stats();
        assert!(stats.current_items <= 2);
    }

    #[test]
    fn test_icon_validation() {
        // 有效图标
        let valid_icon = TrayIcon::new(
            "valid",
            TrayIconType::Default,
            TrayIconFormat::Png,
            vec![1, 2, 3, 4],
            (16, 16),
        );
        assert!(valid_icon.validate().is_ok());

        // 空数据图标
        let empty_icon = TrayIcon::new(
            "empty",
            TrayIconType::Default,
            TrayIconFormat::Png,
            vec![],
            (16, 16),
        );
        assert!(empty_icon.validate().is_err());

        // 无效尺寸图标
        let invalid_size_icon = TrayIcon::new(
            "invalid",
            TrayIconType::Default,
            TrayIconFormat::Png,
            vec![1, 2, 3, 4],
            (0, 0),
        );
        assert!(invalid_size_icon.validate().is_err());
    }
}