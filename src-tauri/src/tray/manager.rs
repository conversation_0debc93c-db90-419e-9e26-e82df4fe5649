/// 托盘管理器模块
/// 
/// 作为整个托盘系统的核心协调器，负责：
/// - 托盘生命周期管理
/// - 组件协调和集成
/// - 事件分发和处理
/// - 状态同步和管理

use std::sync::{Arc, RwLock};
use tokio::sync::{broadcast, mpsc, Mutex};
use tokio::time::{Duration, Instant};
use serde::{Deserialize, Serialize};

use crate::tray::config::{TrayConfig, TrayConfigBuilder};
use crate::tray::errors::{TrayError, TrayResult};
use crate::tray::events::{TrayEvent, TrayEventHandler, TrayEventDispatcher, TrayEventType};
use crate::tray::icons::{TrayIcon, TrayIconManager, TrayIconType};
use crate::tray::menu::{TrayMenu, TrayMenuItem};
use crate::tray::platform::{PlatformTray, PlatformTrayFactory, PlatformType};
use crate::tray::state::{TrayState, TrayStatus, StateChangeEvent, TrayStateConfig};

/// 托盘管理器构建器
pub struct TrayManagerBuilder {
    /// 配置
    config: Option<TrayConfig>,
    /// 事件处理器列表
    event_handlers: Vec<Box<dyn TrayEventHandler>>,
    /// 初始菜单
    initial_menu: Option<TrayMenu>,
    /// 初始图标
    initial_icon: Option<TrayIcon>,
}

impl TrayManagerBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: None,
            event_handlers: Vec::new(),
            initial_menu: None,
            initial_icon: None,
        }
    }

    /// 设置配置
    pub fn with_config(mut self, config: TrayConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// 添加事件处理器
    pub fn add_event_handler(mut self, handler: Box<dyn TrayEventHandler>) -> Self {
        self.event_handlers.push(handler);
        self
    }

    /// 设置初始菜单
    pub fn with_menu(mut self, menu: TrayMenu) -> Self {
        self.initial_menu = Some(menu);
        self
    }

    /// 设置初始图标
    pub fn with_icon(mut self, icon: TrayIcon) -> Self {
        self.initial_icon = Some(icon);
        self
    }

    /// 构建托盘管理器
    pub async fn build(self) -> TrayResult<TrayManager> {
        let config = self.config.unwrap_or_default();
        let mut manager = TrayManager::new(config).await?;

        // 添加事件处理器
        for handler in self.event_handlers {
            manager.add_event_handler(handler).await?;
        }

        // 设置初始菜单
        if let Some(menu) = self.initial_menu {
            manager.set_menu(menu).await?;
        }

        // 设置初始图标
        if let Some(icon) = self.initial_icon {
            manager.set_icon(icon).await?;
        }

        Ok(manager)
    }
}

impl Default for TrayManagerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 托盘管理器统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrayManagerStats {
    /// 创建时间
    pub created_at: std::time::SystemTime,
    /// 运行时长
    pub uptime: Duration,
    /// 总事件处理数量
    pub total_events_processed: u64,
    /// 错误事件数量
    pub error_events: u64,
    /// 当前事件处理器数量
    pub active_handlers: usize,
    /// 菜单项数量
    pub menu_items_count: usize,
    /// 缓存的图标数量
    pub cached_icons_count: usize,
    /// 平台类型
    pub platform_type: PlatformType,
    /// 最后活动时间
    pub last_activity: std::time::SystemTime,
}

/// 托盘管理器
pub struct TrayManager {
    /// 配置
    config: TrayConfig,
    /// 平台特定实现
    platform_tray: Arc<Mutex<Box<dyn PlatformTray>>>,
    /// 状态管理器
    state_manager: Arc<TrayState>,
    /// 事件分发器
    event_dispatcher: Arc<TrayEventDispatcher>,
    /// 图标管理器
    icon_manager: Arc<TrayIconManager>,
    /// 当前菜单
    current_menu: Arc<RwLock<Option<TrayMenu>>>,
    /// 当前图标ID
    current_icon_id: Arc<RwLock<Option<String>>>,
    /// 事件接收器
    event_receiver: Arc<Mutex<Option<mpsc::Receiver<TrayEvent>>>>,
    /// 事件发送器
    event_sender: mpsc::Sender<TrayEvent>,
    /// 统计信息
    stats: Arc<RwLock<TrayManagerStats>>,
    /// 是否正在运行
    is_running: Arc<RwLock<bool>>,
    /// 创建时间
    created_at: Instant,
}

impl TrayManager {
    /// 创建新的托盘管理器
    pub async fn new(config: TrayConfig) -> TrayResult<Self> {
        // 检查平台支持
        if !PlatformTrayFactory::is_supported() {
            return Err(TrayError::unsupported_platform(
                PlatformType::detect().name(),
                "系统托盘",
            ));
        }

        // 创建平台特定实现
        let platform_tray = Arc::new(Mutex::new(PlatformTrayFactory::create_tray()?));

        // 创建状态管理器
        let state_config = TrayStateConfig {
            enable_persistence: config.state_config.enable_persistence,
            state_file_path: config.state_config.state_file_path.as_ref().map(|p| p.to_string_lossy().to_string()),
            max_history_size: config.state_config.max_history_size,
            auto_save_interval: config.state_config.auto_save_interval.as_secs(),
            enable_change_notifications: true,
        };
        let state_manager = Arc::new(TrayState::new(state_config));

        // 创建事件分发器
        let event_dispatcher = Arc::new(TrayEventDispatcher::new());

        // 创建图标管理器
        let icon_cache_config = crate::tray::icons::IconCacheConfig {
            max_cache_size: config.icon_cache_config.max_cache_size,
            max_cache_items: config.icon_cache_config.max_cache_items,
            cache_ttl: config.icon_cache_config.cache_ttl.as_secs(),
            enable_preload: config.icon_cache_config.enable_preload,
        };
        let icon_manager = Arc::new(TrayIconManager::new(icon_cache_config));

        // 创建事件通道
        let (event_sender, event_receiver) = mpsc::channel(1000);

        let created_at = Instant::now();
        let stats = Arc::new(RwLock::new(TrayManagerStats {
            created_at: std::time::SystemTime::now(),
            uptime: Duration::ZERO,
            total_events_processed: 0,
            error_events: 0,
            active_handlers: 0,
            menu_items_count: 0,
            cached_icons_count: 0,
            platform_type: PlatformType::detect(),
            last_activity: std::time::SystemTime::now(),
        }));

        let manager = Self {
            config,
            platform_tray,
            state_manager,
            event_dispatcher,
            icon_manager,
            current_menu: Arc::new(RwLock::new(None)),
            current_icon_id: Arc::new(RwLock::new(None)),
            event_receiver: Arc::new(Mutex::new(Some(event_receiver))),
            event_sender,
            stats,
            is_running: Arc::new(RwLock::new(false)),
            created_at,
        };

        // 设置初始状态
        manager.state_manager.set_state(TrayStatus::Initialized, Some("托盘管理器已创建".to_string())).await?;

        Ok(manager)
    }

    /// 使用构建器创建托盘管理器
    pub fn builder() -> TrayManagerBuilder {
        TrayManagerBuilder::new()
    }

    /// 初始化托盘
    pub async fn initialize(&self) -> TrayResult<()> {
        self.state_manager.set_state(TrayStatus::Initializing, Some("开始初始化托盘".to_string())).await?;

        // 初始化平台托盘
        {
            let mut platform_tray = self.platform_tray.lock().await;
            platform_tray.initialize(&self.config).await?;
        }

        // 预加载图标
        if let Some(icon_dir) = &self.config.icon_directory {
            self.icon_manager.preload_default_icons(icon_dir).await?;
        }

        // 启动事件处理循环
        self.start_event_loop().await?;

        // 设置为运行状态，这样事件循环可以处理事件
        *self.is_running.write().unwrap() = true;

        self.state_manager.set_state(TrayStatus::Initialized, Some("托盘初始化完成".to_string())).await?;
        Ok(())
    }

    /// 显示托盘
    pub async fn show(&self) -> TrayResult<()> {
        if !self.state_manager.get_current_state().is_operational() {
            return Err(TrayError::invalid_state(
                "operational",
                self.state_manager.get_current_state().as_str(),
            ));
        }

        {
            let mut platform_tray = self.platform_tray.lock().await;
            platform_tray.show().await?;
        }

        self.state_manager.set_state(TrayStatus::Visible, Some("托盘已显示".to_string())).await?;
        *self.is_running.write().unwrap() = true;

        Ok(())
    }

    /// 隐藏托盘
    pub async fn hide(&self) -> TrayResult<()> {
        if !self.state_manager.get_current_state().is_operational() {
            return Err(TrayError::invalid_state(
                "operational",
                self.state_manager.get_current_state().as_str(),
            ));
        }

        {
            let mut platform_tray = self.platform_tray.lock().await;
            platform_tray.hide().await?;
        }

        self.state_manager.set_state(TrayStatus::Hidden, Some("托盘已隐藏".to_string())).await?;
        Ok(())
    }

    /// 设置图标
    pub async fn set_icon(&self, icon: TrayIcon) -> TrayResult<()> {
        // 添加图标到缓存
        let icon_id = icon.id.clone();
        self.icon_manager.add_icon(icon.clone()).await?;

        // 设置为当前图标
        self.icon_manager.set_current_icon(&icon_id)?;
        *self.current_icon_id.write().unwrap() = Some(icon_id);

        // 更新平台托盘
        {
            let mut platform_tray = self.platform_tray.lock().await;
            platform_tray.set_icon(&icon).await?;
        }

        self.update_stats(|stats| {
            stats.cached_icons_count = self.icon_manager.list_icons().len();
            stats.last_activity = std::time::SystemTime::now();
        });

        Ok(())
    }

    /// 设置图标（通过ID）
    pub async fn set_icon_by_id(&self, icon_id: &str) -> TrayResult<()> {
        let icon = self.icon_manager.get_icon(icon_id)
            .ok_or_else(|| TrayError::MenuItemNotFound { id: icon_id.to_string() })?;

        self.set_icon(icon).await
    }

    /// 设置工具提示
    pub async fn set_tooltip(&self, tooltip: &str) -> TrayResult<()> {
        let mut platform_tray = self.platform_tray.lock().await;
        platform_tray.set_tooltip(tooltip).await?;

        self.update_stats(|stats| {
            stats.last_activity = std::time::SystemTime::now();
        });

        Ok(())
    }

    /// 设置菜单
    pub async fn set_menu(&self, menu: TrayMenu) -> TrayResult<()> {
        // 验证菜单
        menu.validate()?;

        // 更新平台托盘
        {
            let mut platform_tray = self.platform_tray.lock().await;
            platform_tray.set_menu(&menu).await?;
        }

        // 保存当前菜单
        *self.current_menu.write().unwrap() = Some(menu.clone());

        self.update_stats(|stats| {
            stats.menu_items_count = menu.get_all_item_ids().len();
            stats.last_activity = std::time::SystemTime::now();
        });

        Ok(())
    }

    /// 获取当前菜单
    pub fn get_current_menu(&self) -> Option<TrayMenu> {
        self.current_menu.read().unwrap().clone()
    }

    /// 添加事件处理器
    pub async fn add_event_handler(&self, handler: Box<dyn TrayEventHandler>) -> TrayResult<()> {
        self.event_dispatcher.register_handler(Arc::from(handler)).await?;

        let handler_count = self.event_dispatcher.handler_count().await;
        self.update_stats(|stats| {
            stats.active_handlers = handler_count;
        });

        Ok(())
    }

    /// 移除事件处理器
    pub async fn remove_event_handler(&self, handler_id: &str) -> bool {
        let removed = self.event_dispatcher.unregister_handler(handler_id).await.unwrap_or(false);

        if removed {
            let handler_count = self.event_dispatcher.handler_count().await;
            self.update_stats(|stats| {
                stats.active_handlers = handler_count;
            });
        }

        removed
    }

    /// 发送事件
    pub async fn send_event(&self, event: TrayEvent) -> TrayResult<()> {
        self.event_sender.send(event).await.map_err(|e| {
            TrayError::event_handling_failed("send", format!("发送事件失败: {}", e))
        })?;

        Ok(())
    }

    /// 获取状态管理器
    pub fn get_state_manager(&self) -> Arc<TrayState> {
        self.state_manager.clone()
    }

    /// 获取图标管理器
    pub fn get_icon_manager(&self) -> Arc<TrayIconManager> {
        self.icon_manager.clone()
    }

    /// 获取当前图标ID
    pub fn get_current_icon_id(&self) -> Option<String> {
        self.current_icon_id.read().unwrap().clone()
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> TrayManagerStats {
        let mut stats = self.stats.read().unwrap().clone();
        stats.uptime = self.created_at.elapsed();
        stats
    }

    /// 检查是否正在运行
    pub fn is_running(&self) -> bool {
        *self.is_running.read().unwrap()
    }

    /// 获取当前状态
    pub fn get_current_status(&self) -> TrayStatus {
        self.state_manager.get_current_state()
    }

    /// 订阅状态变更事件
    pub fn subscribe_state_changes(&self) -> broadcast::Receiver<StateChangeEvent> {
        self.state_manager.subscribe_state_changes()
    }

    /// 重新加载配置
    pub async fn reload_config(&self, new_config: TrayConfig) -> TrayResult<()> {
        // 验证新配置
        new_config.validate()?;

        // 这里可以实现配置热重载逻辑
        // 目前简化处理，返回成功
        Ok(())
    }

    /// 销毁托盘
    pub async fn destroy(&self) -> TrayResult<()> {
        self.state_manager.set_state(TrayStatus::Destroying, Some("开始销毁托盘".to_string())).await?;

        // 停止运行
        *self.is_running.write().unwrap() = false;

        // 销毁平台托盘
        {
            let mut platform_tray = self.platform_tray.lock().await;
            platform_tray.destroy().await?;
        }

        // 清理资源
        self.icon_manager.clear_cache();

        self.state_manager.set_state(TrayStatus::Destroyed, Some("托盘已销毁".to_string())).await?;
        Ok(())
    }

    /// 启动事件处理循环
    async fn start_event_loop(&self) -> TrayResult<()> {
        // 检查事件接收器是否已经被使用
        let receiver_option = self.event_receiver.lock().await.take();
        if receiver_option.is_none() {
            // 事件循环已经启动，跳过
            return Ok(());
        }
        
        let mut receiver = receiver_option.unwrap();

        let event_dispatcher = self.event_dispatcher.clone();
        let stats = self.stats.clone();
        let is_running = self.is_running.clone();

        tokio::spawn(async move {
            while let Some(event) = receiver.recv().await {
                if !*is_running.read().unwrap() {
                    continue; // 改为 continue 而不是 break，这样可以继续处理事件
                }

                // 分发事件
                if let Err(e) = event_dispatcher.dispatch_event(event).await {
                    eprintln!("事件处理失败: {}", e);
                    
                    // 更新错误统计
                    let mut stats_guard = stats.write().unwrap();
                    stats_guard.error_events += 1;
                }

                // 更新统计
                let mut stats_guard = stats.write().unwrap();
                stats_guard.total_events_processed += 1;
                stats_guard.last_activity = std::time::SystemTime::now();
            }
        });

        Ok(())
    }

    /// 更新统计信息
    fn update_stats<F>(&self, updater: F)
    where
        F: FnOnce(&mut TrayManagerStats),
    {
        let mut stats = self.stats.write().unwrap();
        updater(&mut stats);
    }
}

/// 为托盘管理器实现 Drop trait，确保资源正确清理
impl Drop for TrayManager {
    fn drop(&mut self) {
        // 在实际应用中，这里可能需要异步清理
        // 目前只是标记状态
        *self.is_running.write().unwrap() = false;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tray::config::TrayConfigBuilder;
    use crate::tray::events::SimpleEventHandler;
    use crate::tray::icons::{TrayIconFormat, TrayIconType};

    #[tokio::test]
    async fn test_tray_manager_creation() {
        let config = TrayConfigBuilder::new()
            .title("Test Tray")
            .tooltip("测试托盘")
            .icon_data("test_icon_data".to_string())
            .build().unwrap();

        let result = TrayManager::new(config).await;
        
        // 在支持的平台上应该能够创建管理器
        if PlatformTrayFactory::is_supported() {
            assert!(result.is_ok());
            let manager = result.unwrap();
            assert_eq!(manager.get_current_status(), TrayStatus::Initialized);
        }
    }

    #[tokio::test]
    async fn test_tray_manager_builder() {
        let config = TrayConfigBuilder::new()
            .title("Test Tray")
            .icon_data("test_icon_data".to_string())
            .build().unwrap();

        let handler = Box::new(SimpleEventHandler::new(
            "test_handler",
            vec![TrayEventType::LeftClick],
            100,
        ));
        
        let result = TrayManagerBuilder::new()
            .with_config(config)
            .add_event_handler(handler)
            .build()
            .await;

        if PlatformTrayFactory::is_supported() {
            assert!(result.is_ok());
            let manager = result.unwrap();
            let stats = manager.get_stats();
            assert_eq!(stats.active_handlers, 1);
        }
    }

    #[tokio::test]
    async fn test_icon_management() {
        if !PlatformTrayFactory::is_supported() {
            return;
        }

        let config = TrayConfigBuilder::new()
            .icon_data("test_icon_data".to_string())
            .build().unwrap();
        let manager = TrayManager::new(config).await.unwrap();

        // 创建测试图标
        let icon = TrayIcon::new(
            "test_icon",
            TrayIconType::Default,
            TrayIconFormat::Png,
            vec![1, 2, 3, 4],
            (16, 16),
        );

        // 先初始化托盘
        manager.initialize().await.unwrap();

        // 设置图标
        let result = manager.set_icon(icon).await;
        assert!(result.is_ok());

        // 检查统计信息
        let stats = manager.get_stats();
        assert!(stats.cached_icons_count > 0);
    }

    #[tokio::test]
    async fn test_state_management() {
        if !PlatformTrayFactory::is_supported() {
            return;
        }

        let config = TrayConfigBuilder::new()
            .icon_data("test_icon_data".to_string())
            .build().unwrap();
        let manager = TrayManager::new(config).await.unwrap();

        // 初始状态应该是 Initialized
        assert_eq!(manager.get_current_status(), TrayStatus::Initialized);

        // 订阅状态变更
        let mut state_receiver = manager.subscribe_state_changes();

        // 初始化托盘
        manager.initialize().await.unwrap();

        // 应该收到状态变更事件
        let event = tokio::time::timeout(Duration::from_millis(100), state_receiver.recv()).await;
        assert!(event.is_ok());
    }

    #[tokio::test]
    async fn test_lifecycle() {
        if !PlatformTrayFactory::is_supported() {
            return;
        }

        let config = TrayConfigBuilder::new()
            .icon_data("test_icon_data".to_string())
            .build().unwrap();
        let manager = TrayManager::new(config).await.unwrap();

        // 初始化
        manager.initialize().await.unwrap();
        assert_eq!(manager.get_current_status(), TrayStatus::Initialized);

        // 显示
        manager.show().await.unwrap();
        assert_eq!(manager.get_current_status(), TrayStatus::Visible);
        assert!(manager.is_running());

        // 隐藏
        manager.hide().await.unwrap();
        assert_eq!(manager.get_current_status(), TrayStatus::Hidden);

        // 销毁
        manager.destroy().await.unwrap();
        assert_eq!(manager.get_current_status(), TrayStatus::Destroyed);
        assert!(!manager.is_running());
    }
} 