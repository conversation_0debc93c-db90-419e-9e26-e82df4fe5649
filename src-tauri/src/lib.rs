// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/

pub mod async_handler; // 添加异步处理器模块
pub mod auth;
pub mod config; // 配置管理模块
pub mod crypto; // 更改为新的模块化加密系统
mod errors;
pub mod http;
pub mod hybrid_storage; // 已更新为使用新的加密系统
pub mod native_messaging; // 模块化的 Native Messaging
mod state; // 认证模块
pub mod sync; // 独立模块化的日志同步系统
pub mod tray; // 跨平台可插拔托盘模块
pub mod tray_integration; // 托盘集成模块

use crate::config::CONFIG;
use state::AppState;
use tauri::{Emitter, Manager, WebviewUrl, WebviewWindowBuilder};
use tauri_plugin_single_instance;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化配置（这会触发环境变量加载）
    let _config = &*CONFIG;
    log::info!("应用配置已加载，服务端地址: {}", _config.server_base_url);

    // 初始化日志记录器 (在开发时很有用)
    env_logger::init();

    let app_state = AppState::new();

    tauri::Builder::default()
        .plugin(tauri_plugin_store::Builder::new().build())
        .plugin(tauri_plugin_stronghold::Builder::new(|_pass| todo!()).build())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_single_instance::init(|app, args, cwd| {
            log::info!(
                "Another instance launched with args: {:?}, cwd: {}",
                args,
                cwd
            );
            // Bring the existing window to the front
            if let Some(window) = app.get_webview_window("main") {
                let _ = window.unminimize();
                let _ = window.set_focus();
            }
        }))
        .manage(app_state)
        .setup(|app| {
            // 在 setup 闭包中获取 AppHandle 并启动 Native Messaging
            let app_handle = app.handle().clone();

            // 使用新的模块化 Native Messaging 系统
            if let Err(e) = native_messaging::setup_native_messaging(app_handle.clone()) {
                log::error!("启动 Native Messaging 失败: {}", e);
            } else {
                log::info!("Native Messaging 服务已启动");
            }

            // 根据不同平台创建不同配置的窗口
            #[cfg(target_os = "macos")]
            let win_builder = {
                let builder = WebviewWindowBuilder::new(app, "main", WebviewUrl::default())
                    .title("Secure Vault")
                    .inner_size(800.0, 600.0)
                    .center()
                    .decorations(true)
                    .hidden_title(true) // 隐藏标题文本
                    .fullscreen(false)
                    .min_inner_size(520.0, 520.0)
                    .visible(false);

                // 尝试设置标题栏样式
                // 注意：根据Tauri版本不同，此API可能有变化
                // 如果编译出错，请注释掉下面这行
                let builder = builder.title_bar_style(tauri::TitleBarStyle::Overlay);

                builder
            };

            #[cfg(not(target_os = "macos"))]
            let win_builder = WebviewWindowBuilder::new(app, "main", WebviewUrl::default())
                .title("Secure Vault")
                .center()
                .fullscreen(false)
                .inner_size(800.0, 600.0)
                .min_inner_size(520.0, 520.0)
                .visible(false)
                .decorations(false);

            let window = win_builder.build();
            let main_window = match window {
                Ok(window) => {
                    window.show().unwrap();
                    Some(window)
                }
                Err(e) => {
                    log::error!("Failed to create window: {}", e);
                    None
                }
            };

            // 在应用启动时初始化 ORM 服务和 Token 管理器
            let handle = app.handle().clone();
            let main_window = handle.get_webview_window("main").clone(); // 提前获取主窗口句柄

            // 使用 async_handler 来运行异步初始化代码
            async_handler::AsyncTaskManager::spawn_task(async move {
                // 在异步任务中获取 app_state
                let app_state = handle.state::<AppState>().clone();

                log::info!("设置 AppHandle 到应用状态...");

                // 首先设置 AppHandle 到应用状态（这会自动加载用户信息）
                app_state.set_app_handle(handle.clone()).await;

                log::info!("AppHandle 已设置，开始初始化 ORM 服务...");

                // 初始化 ORM 服务
                match initialize_orm_service(&handle, &app_state).await {
                    Ok(_) => {
                        log::info!("ORM 服务初始化成功");

                        // 验证 ORM 服务是否正确设置
                        let service_guard = app_state.orm_service_lock().await;
                        if service_guard.is_some() {
                            log::info!("ORM 服务已正确设置到应用状态中");
                        } else {
                            log::error!("ORM 服务初始化成功但未正确设置到应用状态中");
                        }
                    }
                    Err(e) => {
                        log::error!("ORM 服务初始化失败: {}", e);

                        // 检查主窗口是否可用并显示错误提示
                        if let Some(window) = main_window.as_ref() {
                            let _ = window.eval(&format!(
                                "alert('ORM service initialization failed: {}')",
                                e
                            ));
                        }

                        // 可选：退出应用
                        // handle.exit(1);
                    }
                }

                log::info!("开始初始化 Token 管理器...");

                // 初始化 Token 管理器
                if let Err(e) =
                    auth::token_manager::initialize_global_token_manager(handle.clone()).await
                {
                    log::error!("Failed to initialize token manager: {}", e);
                } else {
                    log::info!("Token manager initialized successfully.");
                }

                // 检查是否有已登录的用户
                if app_state.has_logged_in_user().await {
                    let user = app_state.get_current_user().await;
                    if let Some(user) = user {
                        log::info!("发现已登录用户: {}", user.contact);

                        // 通知前端用户已登录
                        if let Some(window) = main_window.as_ref() {
                            let _ = window.emit("user_logged_in", &user);
                        }
                    }
                } else {
                    log::info!("没有发现已登录的用户");
                }

                // 初始化托盘集成
                log::info!("开始初始化托盘集成...");
                match tray_integration::AppTrayIntegration::new(handle.clone(), Some("main".to_string())).await {
                    Ok(tray_integration) => {
                        if let Err(e) = tray_integration.initialize().await {
                            log::error!("托盘集成初始化失败: {}", e);
                        } else {
                            log::info!("托盘集成初始化成功");

                            // 设置窗口关闭行为
                            if let Some(window) = main_window.as_ref() {
                                let tray_integration_arc = std::sync::Arc::new(tray_integration);
                                if let Err(e) = tray_integration::setup_window_close_behavior(window, tray_integration_arc.clone()) {
                                    log::error!("设置窗口关闭行为失败: {}", e);
                                } else {
                                    log::info!("窗口关闭行为设置成功");
                                }

                                // 将托盘集成实例存储到应用状态中，以便后续使用
                                handle.manage(tray_integration_arc);
                            }
                        }
                    }
                    Err(e) => {
                        log::error!("创建托盘集成失败: {}", e);
                    }
                }

                // (可选) 初始化完成后通知前端
                if let Some(window) = main_window.as_ref() {
                    let _ = window.emit("app_ready", ());
                }

                log::info!("应用初始化完成");
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Hybrid Storage 命令
            hybrid_storage::commands::get_all_vaults_hybrid,
            hybrid_storage::commands::create_vault_hybrid,
            hybrid_storage::commands::update_vault_hybrid,
            hybrid_storage::commands::delete_vault_hybrid,
            hybrid_storage::commands::get_login_credentials_by_vault_hybrid,
            hybrid_storage::commands::get_all_login_credentials_hybrid,
            hybrid_storage::commands::get_login_credential_by_id_hybrid,
            hybrid_storage::commands::save_login_credential_hybrid,
            hybrid_storage::commands::add_login_credential_hybrid,
            hybrid_storage::commands::update_login_credential_hybrid,
            hybrid_storage::commands::delete_login_credential_hybrid,
            hybrid_storage::commands::search_credentials_by_domain_hybrid,
            hybrid_storage::commands::get_favorite_credentials_hybrid,
            hybrid_storage::commands::get_all_favorite_credentials_hybrid,
            // Hybrid Storage 软删除命令
            hybrid_storage::commands::soft_delete_login_credential_hybrid,
            hybrid_storage::commands::restore_login_credential_hybrid,
            hybrid_storage::commands::get_trash_login_credentials_hybrid,
            hybrid_storage::commands::get_all_trash_login_credentials_hybrid,
            hybrid_storage::commands::permanently_delete_login_credential_hybrid,
            hybrid_storage::commands::cleanup_expired_deleted_items_hybrid,
            hybrid_storage::commands::get_items_pending_permanent_deletion_count_hybrid,
            // Hybrid Storage 封存命令
            hybrid_storage::commands::archive_login_credential_hybrid,
            hybrid_storage::commands::unarchive_login_credential_hybrid,
            hybrid_storage::commands::get_archived_login_credentials_hybrid,
            hybrid_storage::commands::get_all_archived_login_credentials_hybrid,
            hybrid_storage::commands::batch_archive_login_credentials_hybrid,
            hybrid_storage::commands::batch_unarchive_login_credentials_hybrid,
            // 认证相关命令
            auth::commands::send_verification_code,
            auth::commands::validate_password_strength,
            auth::commands::check_username_availability,
            auth::commands::check_contact_availability,
            auth::commands::validate_password_hint,
            // Auth 模块命令 - 注册功能（新流程）
            auth::commands::register_user_remote_only,
            // Auth 模块命令 - 高安全性功能
            auth::commands::hash_password_secure,
            auth::commands::validate_password_strength_enhanced,
            auth::commands::create_user_vault,
            auth::commands::check_keychain_status,
            auth::commands::get_kdf_recommendations,
            // Auth 模块命令 - 远程认证功能
            auth::commands::register_user_remote,
            auth::commands::login_user_remote,
            auth::commands::test_remote_connection,
            auth::commands::get_remote_server_status,
            auth::commands::register_complete_flow,
            auth::commands::login_user_universal,
            // Auth 模块命令 - Token 管理功能
            auth::commands::get_current_token,
            auth::commands::get_token_info,
            auth::commands::clear_token,
            auth::commands::is_token_expiring_soon,
            auth::commands::refresh_access_token,
            // Auth 模块命令 - 密钥管理功能
            auth::commands::check_user_keys_status,
            auth::commands::cleanup_user_keys,
            auth::commands::generate_test_keypair,
            // Auth 模块命令 - 远程密钥同步功能
            auth::commands::sync_remote_keys,
            auth::commands::get_remote_keys_info,
            // Auth 模块命令 - 用户状态管理功能
            auth::commands::get_current_user_info,
            auth::commands::is_user_logged_in,
            auth::commands::logout_current_user,
            auth::commands::reload_user_info,
            // 托盘集成命令
            tray_integration::show_main_window_command,
            tray_integration::hide_to_tray_command,
            tray_integration::quit_application_command,
            tray_integration::is_tray_available_command,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

/// 初始化 ORM 服务
async fn initialize_orm_service(
    app_handle: &tauri::AppHandle,
    app_state: &AppState,
) -> Result<(), String> {
    use crate::hybrid_storage::{OrmDatabaseManager, OrmPasswordService};

    log::info!("开始获取应用数据目录...");

    // 获取应用数据目录
    let app_data_dir = app_handle
        .path()
        .app_local_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;

    log::info!("应用数据目录: {:?}", app_data_dir);

    // 确保目录存在
    std::fs::create_dir_all(&app_data_dir)
        .map_err(|e| format!("Failed to create app data dir: {}", e))?;

    log::info!("应用数据目录创建成功");

    // 创建数据库路径
    let db_path = app_data_dir.join("hybrid_vault.db");
    log::info!("数据库路径: {:?}", db_path);

    // 检查加密系统状态
    log::info!("检查加密系统状态...");
    let crypto_arc = app_state.crypto_arc();

    // 检查加密系统是否已初始化
    let crypto_state = crypto_arc.state().await;
    log::info!("当前加密系统状态: {:?}", crypto_state);

    // 如果加密系统未初始化，则使用默认密码初始化
    match crypto_state {
        crate::crypto::vault_crypto::CryptoState::Uninitialized => {
            log::info!("加密系统未初始化，使用默认配置初始化...");

            // 生成一个有效的盐值
            let salt = match crate::crypto::key_derivation::generate_salt() {
                Ok(s) => s,
                Err(e) => {
                    log::error!("生成盐值失败: {}", e);
                    return Err(format!("Failed to generate salt: {}", e));
                }
            };

            log::info!("生成的盐值: {}", salt);
            let default_password = "default_password_123!"; // 在生产环境中应该要求用户设置密码

            crypto_arc.initialize(default_password, &salt)
                .await
                .map_err(|e| format!("Failed to initialize crypto system: {}", e))?;

            log::info!("加密系统初始化成功");
        }
        crate::crypto::vault_crypto::CryptoState::Locked => {
            log::info!("加密系统已锁定，尝试解锁...");

            // 这里需要使用相同的盐值，但在实际应用中应该从配置或数据库中读取
            log::warn!("加密系统已锁定，但我们没有存储的盐值，无法解锁");
            return Err("Crypto system is locked but no stored salt available".to_string());
        }
        crate::crypto::vault_crypto::CryptoState::Unlocked => {
            log::info!("加密系统已解锁，可以正常使用");
        }
        crate::crypto::vault_crypto::CryptoState::Error(ref err) => {
            return Err(format!("Crypto system is in error state: {}", err));
        }
    }

    log::info!("加密系统状态检查完成");

    // 初始化 ORM 数据库管理器
    log::info!("开始初始化 ORM 数据库管理器...");
    let db_manager = OrmDatabaseManager::new(db_path)
        .await
        .map_err(|e| format!("Failed to initialize ORM database: {}", e))?;

    log::info!("ORM 数据库管理器初始化成功");

    // 创建 ORM 密码服务
    log::info!("创建 ORM 密码服务...");
    let orm_service = OrmPasswordService::new(db_manager);

    log::info!("ORM 密码服务创建成功");

    // 设置到应用状态
    log::info!("设置 ORM 服务到应用状态...");
    app_state.set_orm_service(orm_service).await;

    log::info!("ORM service initialized successfully");
    Ok(())
}
