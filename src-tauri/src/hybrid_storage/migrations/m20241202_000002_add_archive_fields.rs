use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 为 items 表添加 is_archived 字段
        manager
            .alter_table(
                Table::alter()
                    .table(Items::Table)
                    .add_column(
                        ColumnDef::new(Items::IsArchived)
                            .boolean()
                            .not_null()
                            .default(false)
                    )
                    .to_owned(),
            )
            .await?;

        // 为 items 表添加 archived_at 字段
        manager
            .alter_table(
                Table::alter()
                    .table(Items::Table)
                    .add_column(
                        ColumnDef::new(Items::ArchivedAt)
                            .timestamp()
                            .null()
                    )
                    .to_owned(),
            )
            .await?;

        // 创建索引以提高封存查询性能
        manager
            .create_index(
                Index::create()
                    .name("idx_items_is_archived")
                    .table(Items::Table)
                    .col(Items::IsArchived)
                    .to_owned(),
            )
            .await?;

        manager
            .create_index(
                Index::create()
                    .name("idx_items_archived_at")
                    .table(Items::Table)
                    .col(Items::ArchivedAt)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 删除索引
        manager
            .drop_index(
                Index::drop()
                    .name("idx_items_archived_at")
                    .table(Items::Table)
                    .to_owned(),
            )
            .await?;

        manager
            .drop_index(
                Index::drop()
                    .name("idx_items_is_archived")
                    .table(Items::Table)
                    .to_owned(),
            )
            .await?;

        // 删除 archived_at 列
        manager
            .alter_table(
                Table::alter()
                    .table(Items::Table)
                    .drop_column(Items::ArchivedAt)
                    .to_owned(),
            )
            .await?;

        // 删除 is_archived 列
        manager
            .alter_table(
                Table::alter()
                    .table(Items::Table)
                    .drop_column(Items::IsArchived)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }
}

#[derive(DeriveIden)]
enum Items {
    Table,
    IsArchived,
    ArchivedAt,
} 