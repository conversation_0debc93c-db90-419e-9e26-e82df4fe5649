/// Tray 模块集成示例
/// 
/// 本文件展示了如何在 Tauri 应用中集成和使用 tray 模块

use crate::tray::{
    TrayConfig, TrayManager, TrayManagerBuilder, TrayMenuItem, TrayMenuBuilder,
    TrayEventHandler, TrayEvent, TrayEventType, TrayError, TrayResult
};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use std::sync::Arc;
use tokio::sync::Mutex;

/// 应用托盘事件处理器
/// 
/// 处理来自系统托盘的各种事件
pub struct AppTrayEventHandler {
    app_handle: AppHandle,
}

impl AppTrayEventHandler {
    /// 创建新的应用托盘事件处理器
    /// 
    /// # 参数
    /// 
    /// * `app_handle` - Tauri 应用句柄
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle }
    }
}

#[async_trait::async_trait]
impl TrayEventHandler for AppTrayEventHandler {
    /// 获取处理器名称
    fn name(&self) -> &str {
        "AppTrayEventHandler"
    }

    /// 处理托盘事件
    /// 
    /// # 参数
    /// 
    /// * `event` - 托盘事件
    async fn handle_event(&self, event: &TrayEvent) -> TrayResult<()> {
        match &event.event_type {
            TrayEventType::LeftClick => {
                log::info!("托盘左键点击事件");
                self.show_main_window().await?;
            }
            TrayEventType::RightClick => {
                log::info!("托盘右键点击事件");
                // 右键点击通常显示上下文菜单，这由系统自动处理
            }
            TrayEventType::DoubleClick => {
                log::info!("托盘双击事件");
                self.show_main_window().await?;
            }
            TrayEventType::MenuItemSelected => {
                if let Some(menu_id) = &event.menu_item_id {
                    log::info!("菜单项点击: {}", menu_id);
                    self.handle_menu_click(menu_id).await?;
                }
            }
            TrayEventType::MiddleClick => {
                log::info!("托盘中键点击事件");
            }
            TrayEventType::MouseEnter => {
                log::debug!("鼠标进入托盘图标区域");
            }
            TrayEventType::MouseLeave => {
                log::debug!("鼠标离开托盘图标区域");
            }
            TrayEventType::MouseMove => {
                // 鼠标移动事件通常不需要处理
            }
            TrayEventType::TrayCreated => {
                log::info!("托盘图标已创建");
            }
            TrayEventType::TrayDestroyed => {
                log::info!("托盘图标已销毁");
            }
            TrayEventType::TrayUpdated => {
                log::info!("托盘图标已更新");
            }
            TrayEventType::SystemThemeChanged => {
                log::info!("系统主题已变更");
                // 可以在这里更新图标以适应新主题
            }
            TrayEventType::Custom(event_name) => {
                log::info!("自定义事件: {}", event_name);
            }
        }
        Ok(())
    }
}

impl AppTrayEventHandler {
    /// 显示主窗口
    async fn show_main_window(&self) -> TrayResult<()> {
        if let Some(window) = self.app_handle.get_webview_window("main") {
            window.show().map_err(|e| TrayError::internal(e.to_string()))?;
            window.set_focus().map_err(|e| TrayError::internal(e.to_string()))?;
            window.unminimize().map_err(|e| TrayError::internal(e.to_string()))?;
        }
        Ok(())
    }

    /// 处理菜单点击事件
    async fn handle_menu_click(&self, menu_id: &str) -> TrayResult<()> {
        match menu_id {
            "show" => {
                self.show_main_window().await?;
            }
            "hide" => {
                if let Some(window) = self.app_handle.get_webview_window("main") {
                    window.hide().map_err(|e| TrayError::internal(e.to_string()))?;
                }
            }
            "settings" => {
                // 打开设置页面
                if let Some(window) = self.app_handle.get_webview_window("main") {
                    window.eval("window.location.hash = '#/settings'")
                        .map_err(|e| TrayError::internal(e.to_string()))?;
                    window.show().map_err(|e| TrayError::internal(e.to_string()))?;
                }
            }
            "about" => {
                // 显示关于对话框
                if let Some(window) = self.app_handle.get_webview_window("main") {
                    window.eval("alert('Secure Password v1.0.0\\n安全密码管理器')")
                        .map_err(|e| TrayError::internal(e.to_string()))?;
                }
            }
            "quit" => {
                log::info!("用户请求退出应用");
                self.app_handle.exit(0);
            }
            _ => {
                log::warn!("未知的菜单项ID: {}", menu_id);
            }
        }
        Ok(())
    }
}

/// 创建应用托盘菜单
/// 
/// # 返回值
/// 
/// 返回配置好的托盘菜单
pub fn create_app_tray_menu() -> TrayResult<crate::tray::TrayMenu> {
    let mut builder = TrayMenuBuilder::new();
    builder = builder.add_item(TrayMenuItem::new("show", "显示主窗口"))?;
    builder = builder.add_item(TrayMenuItem::new("hide", "隐藏窗口"))?;
    builder = builder.add_separator()?;
    builder = builder.add_item(TrayMenuItem::new("settings", "设置"))?;
    builder = builder.add_item(TrayMenuItem::new("about", "关于"))?;
    builder = builder.add_separator()?;
    builder = builder.add_item(TrayMenuItem::new("quit", "退出"))?;
    builder.build()
}

/// 初始化应用托盘
/// 
/// # 参数
/// 
/// * `app_handle` - Tauri 应用句柄
/// 
/// # 返回值
/// 
/// 返回托盘管理器实例
pub async fn initialize_app_tray(app_handle: AppHandle) -> TrayResult<Arc<Mutex<TrayManager>>> {
    log::info!("开始初始化应用托盘");

    // 创建托盘配置
    let mut config_builder = TrayConfig::builder();
    config_builder = config_builder.title("Secure Password");
    config_builder = config_builder.tooltip("安全密码管理器 - 点击显示主窗口");
    config_builder = config_builder.icon_path("icons/tray-icon.png");
    let config = config_builder.build()?;

    // 创建事件处理器
    let event_handler = AppTrayEventHandler::new(app_handle.clone());

    // 创建托盘管理器
    let mut manager_builder = TrayManagerBuilder::new();
    manager_builder = manager_builder.with_config(config);
    manager_builder = manager_builder.add_event_handler(Box::new(event_handler));
    let tray_manager = manager_builder.build().await?;

    // 显示托盘
    tray_manager.show().await?;

    log::info!("应用托盘初始化完成");

    Ok(Arc::new(Mutex::new(tray_manager)))
}

/// 更新托盘图标状态
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器
/// * `is_locked` - 是否处于锁定状态
pub async fn update_tray_icon_status(
    tray_manager: &Arc<Mutex<TrayManager>>,
    is_locked: bool,
) -> TrayResult<()> {
    let manager = tray_manager.lock().await;
    
    let icon_path = if is_locked {
        "icons/tray-icon-locked.png"
    } else {
        "icons/tray-icon-unlocked.png"
    };

    let tooltip = if is_locked {
        "安全密码管理器 - 已锁定"
    } else {
        "安全密码管理器 - 已解锁"
    };

    manager.set_icon_by_id(icon_path).await?;
    manager.set_tooltip(tooltip).await?;

    Ok(())
}

/// 显示托盘通知
/// 
/// # 参数
/// 
/// * `tray_manager` - 托盘管理器
/// * `title` - 通知标题
/// * `message` - 通知消息
pub async fn show_tray_notification(
    tray_manager: &Arc<Mutex<TrayManager>>,
    title: &str,
    message: &str,
) -> TrayResult<()> {
    let manager = tray_manager.lock().await;
    // TODO: 需要在 TrayManager 中实现通知功能
    // manager.show_notification(title, message).await?;
    Ok(())
}

/// 在 Tauri setup 中集成托盘的示例代码
/// 
/// 这个函数展示了如何在 Tauri 应用的 setup 回调中初始化托盘
pub async fn setup_tray_in_tauri_app(app_handle: AppHandle) -> TrayResult<()> {
    // 初始化托盘
    let tray_manager = initialize_app_tray(app_handle.clone()).await?;

    // 将托盘管理器存储到应用状态中
    app_handle.manage(tray_manager.clone());

    // 监听应用状态变化，更新托盘图标
    let tray_for_status = tray_manager.clone();
    tokio::spawn(async move {
        // 这里可以监听应用状态变化
        // 例如：监听锁定/解锁状态
        loop {
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
            
            // 示例：每5秒检查一次状态并更新图标
            // 在实际应用中，这应该基于真实的状态变化事件
            if let Err(e) = update_tray_icon_status(&tray_for_status, false).await {
                log::error!("更新托盘图标失败: {}", e);
            }
        }
    });

    log::info!("托盘集成完成");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_create_app_tray_menu() {
        let menu = create_app_tray_menu().unwrap();
        assert!(menu.items.len() > 0);
        
        // 检查是否包含预期的菜单项
        let item_ids: Vec<String> = menu.items.iter()
            .map(|item| item.id.clone())
            .collect();
        
        assert!(item_ids.contains(&"show".to_string()));
        assert!(item_ids.contains(&"quit".to_string()));
    }

    #[tokio::test]
    async fn test_app_tray_event_handler_creation() {
        // 注意：这个测试需要模拟的 AppHandle
        // 在实际测试中，你可能需要使用 mock 或者集成测试
        
        // 这里只是展示如何测试事件处理器的创建
        // let app_handle = create_mock_app_handle();
        // let handler = AppTrayEventHandler::new(app_handle);
        // assert!(handler.app_handle.is_some());
    }
} 