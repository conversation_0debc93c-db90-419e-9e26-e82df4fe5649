//! 向量时钟实现
//!
//! 提供分布式系统中的因果关系检测和并发控制功能

use crate::sync::errors::{SyncError, VectorClockError};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::fmt;

/// 向量时钟操作结果
pub type VectorClockResult<T> = Result<T, SyncError>;

/// 向量时钟 - 用于分布式系统中的因果关系检测
///
/// 向量时钟是一种逻辑时钟，用于确定分布式系统中事件的因果关系。
/// 每个设备维护一个向量，记录自己和其他设备的逻辑时间。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct VectorClock {
    /// 设备ID到逻辑时钟值的映射
    /// Key: 设备ID, Value: 该设备的逻辑时钟值
    clocks: HashMap<String, u64>,
}

/// 因果关系枚举
///
/// 描述两个向量时钟之间的因果关系
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CausalRelation {
    /// self 发生在 other 之前（self → other）
    Before,
    /// self 发生在 other 之后（other → self）
    After,
    /// 两个事件相等（self = other）
    Equal,
    /// 两个事件并发（self || other）
    Concurrent,
}

impl VectorClock {
    /// 创建新的空向量时钟
    ///
    /// # Examples
    ///
    /// ```
    /// use crate::sync::VectorClock;
    ///
    /// let clock = VectorClock::new();
    /// assert!(clock.is_empty());
    /// ```
    pub fn new() -> Self {
        Self {
            clocks: HashMap::new(),
        }
    }

    /// 从设备列表创建向量时钟，所有设备的时钟值初始化为0
    ///
    /// # Arguments
    ///
    /// * `device_ids` - 设备ID列表
    ///
    /// # Examples
    ///
    /// ```
    /// use crate::sync::VectorClock;
    ///
    /// let devices = vec!["device1".to_string(), "device2".to_string()];
    /// let clock = VectorClock::from_devices(&devices);
    /// assert_eq!(clock.get_clock("device1"), 0);
    /// assert_eq!(clock.get_clock("device2"), 0);
    /// ```
    pub fn from_devices(device_ids: &[String]) -> Self {
        let mut clocks = HashMap::new();
        for device_id in device_ids {
            clocks.insert(device_id.clone(), 0);
        }
        Self { clocks }
    }

    /// 递增指定设备的时钟值
    ///
    /// 当设备执行本地操作时调用此方法
    ///
    /// # Arguments
    ///
    /// * `device_id` - 要递增时钟的设备ID
    ///
    /// # Returns
    ///
    /// 返回递增后的时钟值
    ///
    /// # Errors
    ///
    /// 如果设备ID为空，返回 `VectorClockError::EmptyDeviceId`
    /// 如果时钟值溢出，返回 `VectorClockError::ClockOverflow`
    pub fn tick(&mut self, device_id: &str) -> VectorClockResult<u64> {
        if device_id.is_empty() {
            return Err(VectorClockError::EmptyDeviceId.into());
        }

        let current_value = self.clocks.get(device_id).unwrap_or(&0);

        // 检查溢出
        if *current_value == u64::MAX {
            return Err(VectorClockError::ClockOverflow {
                device_id: device_id.to_string(),
                current_value: *current_value,
            }
            .into());
        }

        let new_value = current_value + 1;
        self.clocks.insert(device_id.to_string(), new_value);
        Ok(new_value)
    }

    /// 更新向量时钟
    ///
    /// 当接收到其他设备的消息时调用此方法，用于同步向量时钟
    ///
    /// # Arguments
    ///
    /// * `other` - 其他设备的向量时钟
    /// * `local_device_id` - 本地设备ID
    ///
    /// # Returns
    ///
    /// 返回更新后本地设备的时钟值
    ///
    /// # Errors
    ///
    /// 如果本地设备ID为空，返回 `VectorClockError::EmptyDeviceId`
    pub fn update(&mut self, other: &VectorClock, local_device_id: &str) -> VectorClockResult<u64> {
        if local_device_id.is_empty() {
            return Err(VectorClockError::EmptyDeviceId.into());
        }

        // 更新所有其他设备的时钟值为最大值
        for (device_id, &other_clock) in &other.clocks {
            if device_id != local_device_id {
                let current_clock = self.clocks.get(device_id).unwrap_or(&0);
                self.clocks
                    .insert(device_id.clone(), (*current_clock).max(other_clock));
            }
        }

        // 递增本地设备的时钟值
        self.tick(local_device_id)
    }

    /// 比较两个向量时钟的因果关系
    ///
    /// # Arguments
    ///
    /// * `other` - 要比较的另一个向量时钟
    ///
    /// # Returns
    ///
    /// 返回因果关系类型
    ///
    /// # Examples
    ///
    /// ```
    /// use crate::sync::{VectorClock, CausalRelation};
    ///
    /// let mut clock1 = VectorClock::new();
    /// let mut clock2 = VectorClock::new();
    ///
    /// clock1.tick("device1").unwrap();
    /// clock2.update(&clock1, "device2").unwrap();
    ///
    /// assert_eq!(clock1.compare(&clock2), CausalRelation::Before);
    /// ```
    pub fn compare(&self, other: &VectorClock) -> CausalRelation {
        let all_devices: HashSet<String> = self
            .clocks
            .keys()
            .chain(other.clocks.keys())
            .cloned()
            .collect();

        let mut self_less_or_equal = true;
        let mut self_greater_or_equal = true;
        let mut has_difference = false;

        for device_id in all_devices {
            let self_clock = self.clocks.get(&device_id).unwrap_or(&0);
            let other_clock = other.clocks.get(&device_id).unwrap_or(&0);

            if self_clock < other_clock {
                self_greater_or_equal = false;
                has_difference = true;
            } else if self_clock > other_clock {
                self_less_or_equal = false;
                has_difference = true;
            }
        }

        match (self_less_or_equal, self_greater_or_equal, has_difference) {
            (true, true, false) => CausalRelation::Equal,
            (true, false, true) => CausalRelation::Before,
            (false, true, true) => CausalRelation::After,
            (false, false, true) => CausalRelation::Concurrent,
            _ => CausalRelation::Equal, // 不应该到达这里
        }
    }

    /// 检查是否与另一个向量时钟并发
    ///
    /// # Arguments
    ///
    /// * `other` - 要检查的另一个向量时钟
    ///
    /// # Returns
    ///
    /// 如果两个向量时钟并发，返回 `true`
    pub fn is_concurrent(&self, other: &VectorClock) -> bool {
        matches!(self.compare(other), CausalRelation::Concurrent)
    }

    /// 检查是否发生在另一个向量时钟之前
    ///
    /// # Arguments
    ///
    /// * `other` - 要检查的另一个向量时钟
    ///
    /// # Returns
    ///
    /// 如果 self 发生在 other 之前，返回 `true`
    pub fn happens_before(&self, other: &VectorClock) -> bool {
        matches!(self.compare(other), CausalRelation::Before)
    }

    /// 检查是否发生在另一个向量时钟之后
    ///
    /// # Arguments
    ///
    /// * `other` - 要检查的另一个向量时钟
    ///
    /// # Returns
    ///
    /// 如果 self 发生在 other 之后，返回 `true`
    pub fn happens_after(&self, other: &VectorClock) -> bool {
        matches!(self.compare(other), CausalRelation::After)
    }

    /// 获取指定设备的时钟值
    ///
    /// # Arguments
    ///
    /// * `device_id` - 设备ID
    ///
    /// # Returns
    ///
    /// 返回设备的时钟值，如果设备不存在则返回0
    pub fn get_clock(&self, device_id: &str) -> u64 {
        self.clocks.get(device_id).copied().unwrap_or(0)
    }

    /// 获取所有设备ID列表
    ///
    /// # Returns
    ///
    /// 返回所有设备ID的向量
    pub fn get_devices(&self) -> Vec<String> {
        self.clocks.keys().cloned().collect()
    }

    /// 计算所有时钟值的总和
    ///
    /// 用于在并发情况下进行排序的辅助方法
    ///
    /// # Returns
    ///
    /// 返回所有时钟值的总和
    pub fn sum(&self) -> u64 {
        self.clocks.values().sum()
    }

    /// 压缩向量时钟
    ///
    /// 移除不活跃设备的时钟值，减少向量时钟的大小
    ///
    /// # Arguments
    ///
    /// * `active_devices` - 活跃设备ID集合
    ///
    /// # Returns
    ///
    /// 返回被移除的设备数量
    pub fn compress(&mut self, active_devices: &HashSet<String>) -> usize {
        let initial_size = self.clocks.len();
        self.clocks
            .retain(|device_id, _| active_devices.contains(device_id));
        initial_size - self.clocks.len()
    }

    /// 合并另一个向量时钟
    ///
    /// 将另一个向量时钟的所有设备时钟值合并到当前时钟中，
    /// 对于每个设备，取两个时钟中的最大值
    ///
    /// # Arguments
    ///
    /// * `other` - 要合并的向量时钟
    pub fn merge(&mut self, other: &VectorClock) {
        for (device_id, &other_clock) in &other.clocks {
            let current_clock = self.clocks.get(device_id).unwrap_or(&0);
            self.clocks
                .insert(device_id.clone(), (*current_clock).max(other_clock));
        }
    }

    /// 检查向量时钟是否为空
    ///
    /// # Returns
    ///
    /// 如果向量时钟不包含任何设备，返回 `true`
    pub fn is_empty(&self) -> bool {
        self.clocks.is_empty()
    }

    /// 获取向量时钟的大小（设备数量）
    ///
    /// # Returns
    ///
    /// 返回向量时钟中设备的数量
    pub fn size(&self) -> usize {
        self.clocks.len()
    }

    /// 重置向量时钟
    ///
    /// 清空所有设备的时钟值
    pub fn reset(&mut self) {
        self.clocks.clear();
    }

    /// 设置指定设备的时钟值
    ///
    /// # Arguments
    ///
    /// * `device_id` - 设备ID
    /// * `clock_value` - 要设置的时钟值
    ///
    /// # Errors
    ///
    /// 如果设备ID为空，返回 `VectorClockError::EmptyDeviceId`
    pub fn set_clock(&mut self, device_id: &str, clock_value: u64) -> VectorClockResult<()> {
        if device_id.is_empty() {
            return Err(VectorClockError::EmptyDeviceId.into());
        }

        self.clocks.insert(device_id.to_string(), clock_value);
        Ok(())
    }

    /// 检查向量时钟的大小是否超过限制
    ///
    /// # Arguments
    ///
    /// * `max_size` - 最大允许的设备数量
    ///
    /// # Returns
    ///
    /// 如果超过限制，返回 `VectorClockError::SizeExceeded`
    pub fn check_size_limit(&self, max_size: usize) -> VectorClockResult<()> {
        if self.clocks.len() > max_size {
            return Err(VectorClockError::SizeExceeded {
                current_size: self.clocks.len(),
                max_size,
            }
            .into());
        }
        Ok(())
    }

    /// 创建向量时钟的副本，只包含指定的设备
    ///
    /// # Arguments
    ///
    /// * `device_ids` - 要包含的设备ID集合
    ///
    /// # Returns
    ///
    /// 返回新的向量时钟，只包含指定的设备
    pub fn filter_devices(&self, device_ids: &HashSet<String>) -> VectorClock {
        let mut filtered_clocks = HashMap::new();
        for device_id in device_ids {
            if let Some(&clock_value) = self.clocks.get(device_id) {
                filtered_clocks.insert(device_id.clone(), clock_value);
            }
        }
        VectorClock {
            clocks: filtered_clocks,
        }
    }

    /// 获取向量时钟的最大时钟值
    ///
    /// # Returns
    ///
    /// 返回所有设备中的最大时钟值，如果向量时钟为空则返回0
    pub fn max_clock(&self) -> u64 {
        self.clocks.values().copied().max().unwrap_or(0)
    }

    /// 获取向量时钟的最小时钟值
    ///
    /// # Returns
    ///
    /// 返回所有设备中的最小时钟值，如果向量时钟为空则返回0
    pub fn min_clock(&self) -> u64 {
        self.clocks.values().copied().min().unwrap_or(0)
    }
}

impl Default for VectorClock {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for VectorClock {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "VectorClock {{ ")?;
        let mut first = true;
        for (device_id, clock_value) in &self.clocks {
            if !first {
                write!(f, ", ")?;
            }
            write!(f, "{}: {}", device_id, clock_value)?;
            first = false;
        }
        write!(f, " }}")
    }
}

impl fmt::Display for CausalRelation {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CausalRelation::Before => write!(f, "Before"),
            CausalRelation::After => write!(f, "After"),
            CausalRelation::Equal => write!(f, "Equal"),
            CausalRelation::Concurrent => write!(f, "Concurrent"),
        }
    }
}

/// 向量时钟管理器
///
/// 提供向量时钟的高级管理功能，包括自动压缩、统计信息等
pub struct VectorClockManager {
    /// 当前向量时钟
    clock: VectorClock,
    /// 本地设备ID
    local_device_id: String,
    /// 最大设备数量限制
    max_devices: usize,
    /// 压缩阈值
    compression_threshold: usize,
    /// 统计信息
    stats: VectorClockStats,
}

/// 向量时钟统计信息
#[derive(Debug, Clone, Default)]
pub struct VectorClockStats {
    /// tick操作次数
    pub tick_count: u64,
    /// update操作次数
    pub update_count: u64,
    /// compare操作次数
    pub compare_count: u64,
    /// 压缩操作次数
    pub compression_count: u64,
    /// 最后压缩时间
    pub last_compression: Option<chrono::DateTime<chrono::Utc>>,
}

impl VectorClockManager {
    /// 创建新的向量时钟管理器
    ///
    /// # Arguments
    ///
    /// * `local_device_id` - 本地设备ID
    /// * `max_devices` - 最大设备数量限制
    /// * `compression_threshold` - 压缩阈值
    pub fn new(local_device_id: String, max_devices: usize, compression_threshold: usize) -> Self {
        Self {
            clock: VectorClock::new(),
            local_device_id,
            max_devices,
            compression_threshold,
            stats: VectorClockStats::default(),
        }
    }

    /// 执行tick操作
    pub fn tick(&mut self) -> VectorClockResult<u64> {
        let result = self.clock.tick(&self.local_device_id);
        if result.is_ok() {
            self.stats.tick_count += 1;
            self.check_compression_needed();
        }
        result
    }

    /// 执行update操作
    pub fn update(&mut self, other: &VectorClock) -> VectorClockResult<u64> {
        let result = self.clock.update(other, &self.local_device_id);
        if result.is_ok() {
            self.stats.update_count += 1;
            self.check_compression_needed();
        }
        result
    }

    /// 比较向量时钟
    pub fn compare(&mut self, other: &VectorClock) -> CausalRelation {
        self.stats.compare_count += 1;
        self.clock.compare(other)
    }

    /// 获取当前向量时钟的引用
    pub fn get_clock(&self) -> &VectorClock {
        &self.clock
    }

    /// 获取当前向量时钟的副本
    pub fn clone_clock(&self) -> VectorClock {
        self.clock.clone()
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> &VectorClockStats {
        &self.stats
    }

    /// 手动触发压缩
    pub fn compress(&mut self, active_devices: &HashSet<String>) -> usize {
        let removed_count = self.clock.compress(active_devices);
        if removed_count > 0 {
            self.stats.compression_count += 1;
            self.stats.last_compression = Some(chrono::Utc::now());
        }
        removed_count
    }

    /// 检查是否需要压缩
    fn check_compression_needed(&mut self) {
        if self.clock.size() >= self.compression_threshold {
            // 这里可以实现自动压缩逻辑
            // 例如，保留最近活跃的设备
            log::info!("向量时钟大小达到压缩阈值: {}", self.clock.size());
        }
    }

    /// 重置管理器
    pub fn reset(&mut self) {
        self.clock.reset();
        self.stats = VectorClockStats::default();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_vector_clock_creation() {
        let clock = VectorClock::new();
        assert!(clock.is_empty());
        assert_eq!(clock.size(), 0);
    }

    #[test]
    fn test_vector_clock_from_devices() {
        let devices = vec!["device1".to_string(), "device2".to_string()];
        let clock = VectorClock::from_devices(&devices);
        assert_eq!(clock.size(), 2);
        assert_eq!(clock.get_clock("device1"), 0);
        assert_eq!(clock.get_clock("device2"), 0);
    }

    #[test]
    fn test_vector_clock_tick() {
        let mut clock = VectorClock::new();

        // 第一次tick
        let result = clock.tick("device1").unwrap();
        assert_eq!(result, 1);
        assert_eq!(clock.get_clock("device1"), 1);

        // 第二次tick
        let result = clock.tick("device1").unwrap();
        assert_eq!(result, 2);
        assert_eq!(clock.get_clock("device1"), 2);
    }

    #[test]
    fn test_vector_clock_tick_empty_device_id() {
        let mut clock = VectorClock::new();
        let result = clock.tick("");
        assert!(result.is_err());
    }

    #[test]
    fn test_vector_clock_update() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();

        // device1 执行操作
        clock1.tick("device1").unwrap();
        assert_eq!(clock1.get_clock("device1"), 1);

        // device2 接收 device1 的消息并更新
        clock2.update(&clock1, "device2").unwrap();
        assert_eq!(clock2.get_clock("device1"), 1);
        assert_eq!(clock2.get_clock("device2"), 1);
    }

    #[test]
    fn test_causal_relation_equal() {
        let clock1 = VectorClock::new();
        let clock2 = VectorClock::new();
        assert_eq!(clock1.compare(&clock2), CausalRelation::Equal);
    }

    #[test]
    fn test_causal_relation_before() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();

        clock1.tick("device1").unwrap();
        clock2.update(&clock1, "device2").unwrap();

        assert_eq!(clock1.compare(&clock2), CausalRelation::Before);
        assert_eq!(clock2.compare(&clock1), CausalRelation::After);
    }

    #[test]
    fn test_causal_relation_concurrent() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();

        // 两个设备并发执行操作
        clock1.tick("device1").unwrap();
        clock2.tick("device2").unwrap();

        assert_eq!(clock1.compare(&clock2), CausalRelation::Concurrent);
        assert!(clock1.is_concurrent(&clock2));
    }

    #[test]
    fn test_complex_causal_scenario() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();
        let mut clock3 = VectorClock::new();

        // device1 执行操作
        clock1.tick("device1").unwrap();

        // device2 接收 device1 的消息
        clock2.update(&clock1, "device2").unwrap();

        // device3 并发执行操作
        clock3.tick("device3").unwrap();

        // 验证因果关系
        assert_eq!(clock1.compare(&clock2), CausalRelation::Before);
        assert_eq!(clock1.compare(&clock3), CausalRelation::Concurrent);
        assert_eq!(clock2.compare(&clock3), CausalRelation::Concurrent);
    }

    #[test]
    fn test_vector_clock_merge() {
        let mut clock1 = VectorClock::new();
        let mut clock2 = VectorClock::new();

        clock1.tick("device1").unwrap();
        clock1.tick("device1").unwrap();

        clock2.tick("device2").unwrap();
        clock2.tick("device3").unwrap();

        clock1.merge(&clock2);

        assert_eq!(clock1.get_clock("device1"), 2);
        assert_eq!(clock1.get_clock("device2"), 1);
        assert_eq!(clock1.get_clock("device3"), 1);
    }

    #[test]
    fn test_vector_clock_compress() {
        let mut clock = VectorClock::new();

        clock.tick("device1").unwrap();
        clock.tick("device2").unwrap();
        clock.tick("device3").unwrap();

        let mut active_devices = HashSet::new();
        active_devices.insert("device1".to_string());
        active_devices.insert("device2".to_string());

        let removed_count = clock.compress(&active_devices);
        assert_eq!(removed_count, 1);
        assert_eq!(clock.size(), 2);
        assert_eq!(clock.get_clock("device3"), 0); // 被移除的设备返回0
    }

    #[test]
    fn test_vector_clock_sum() {
        let mut clock = VectorClock::new();

        clock.tick("device1").unwrap();
        clock.tick("device1").unwrap();
        clock.tick("device2").unwrap();

        assert_eq!(clock.sum(), 3); // 2 + 1 = 3
    }

    #[test]
    fn test_vector_clock_manager() {
        let mut manager = VectorClockManager::new("device1".to_string(), 100, 50);

        // 执行操作
        manager.tick().unwrap();
        manager.tick().unwrap();

        // 检查统计信息
        let stats = manager.get_stats();
        assert_eq!(stats.tick_count, 2);

        // 检查时钟状态
        assert_eq!(manager.get_clock().get_clock("device1"), 2);
    }

    #[test]
    fn test_vector_clock_display() {
        let mut clock = VectorClock::new();
        clock.tick("device1").unwrap();
        clock.tick("device2").unwrap();

        let display_string = format!("{}", clock);
        assert!(display_string.contains("VectorClock"));
        assert!(display_string.contains("device1"));
        assert!(display_string.contains("device2"));
    }

    #[test]
    fn test_causal_relation_display() {
        assert_eq!(format!("{}", CausalRelation::Before), "Before");
        assert_eq!(format!("{}", CausalRelation::After), "After");
        assert_eq!(format!("{}", CausalRelation::Equal), "Equal");
        assert_eq!(format!("{}", CausalRelation::Concurrent), "Concurrent");
    }

    #[test]
    fn test_vector_clock_serialization() {
        let mut clock = VectorClock::new();
        clock.tick("device1").unwrap();
        clock.tick("device2").unwrap();

        // 序列化
        let json = serde_json::to_string(&clock).unwrap();

        // 反序列化
        let deserialized: VectorClock = serde_json::from_str(&json).unwrap();

        assert_eq!(clock, deserialized);
    }

    #[test]
    fn test_vector_clock_filter_devices() {
        let mut clock = VectorClock::new();
        clock.tick("device1").unwrap();
        clock.tick("device2").unwrap();
        clock.tick("device3").unwrap();

        let mut filter_devices = HashSet::new();
        filter_devices.insert("device1".to_string());
        filter_devices.insert("device3".to_string());

        let filtered = clock.filter_devices(&filter_devices);
        assert_eq!(filtered.size(), 2);
        assert_eq!(filtered.get_clock("device1"), 1);
        assert_eq!(filtered.get_clock("device2"), 0);
        assert_eq!(filtered.get_clock("device3"), 1);
    }

    #[test]
    fn test_vector_clock_max_min() {
        let mut clock = VectorClock::new();
        clock.set_clock("device1", 5).unwrap();
        clock.set_clock("device2", 3).unwrap();
        clock.set_clock("device3", 8).unwrap();

        assert_eq!(clock.max_clock(), 8);
        assert_eq!(clock.min_clock(), 3);

        let empty_clock = VectorClock::new();
        assert_eq!(empty_clock.max_clock(), 0);
        assert_eq!(empty_clock.min_clock(), 0);
    }
}
