//! 同步系统核心数据类型定义
//!
//! 定义了同步系统中使用的所有核心数据结构

use crate::sync::{calculate_data_hash, generate_version, SyncError, VectorClock};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 操作类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OperationType {
    /// 创建操作
    Create,
    /// 更新操作
    Update,
    /// 删除操作
    Delete,
    /// 恢复操作
    Restore,
    /// 移动操作
    Move,
    /// 复制操作
    Copy,
}

/// 同步记录 - 系统中所有数据变更的基本单位
///
/// 每个同步记录代表一次原子性的数据操作，包含完整的元数据信息
/// 用于在多设备间同步数据变更
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct SyncRecord {
    /// 记录的唯一标识符
    pub id: Uuid,

    /// 凭据的唯一标识符
    pub credential_id: String,

    /// 凭据类型
    pub credential_type: CredentialType,

    /// 操作类型（创建、更新、删除等）
    pub operation_type: OperationType,

    /// 凭据数据的JSON表示
    pub data: Option<String>,

    /// 数据的SHA-256哈希值，用于快速比较和冲突检测
    pub data_hash: Option<String>,

    /// 记录版本号，基于时间戳生成，确保单调递增
    pub version: i64,

    /// 创建此记录的设备ID
    pub device_id: String,

    /// 本地创建时间戳
    pub local_timestamp: DateTime<Utc>,

    /// 服务端确认时间戳（NTP同步后的时间）
    pub server_timestamp: Option<DateTime<Utc>>,

    /// 向量时钟，用于因果关系检测
    pub vector_clock: Option<VectorClock>,

    /// 因果依赖的记录ID列表
    pub causal_dependencies: Vec<Uuid>,

    /// 是否已同步到服务端
    pub synced: bool,

    /// 同步重试次数
    pub retry_count: u32,

    /// 记录创建时间
    pub created_at: DateTime<Utc>,

    /// 记录最后更新时间
    pub updated_at: DateTime<Utc>,

    /// 额外的元数据
    pub metadata: HashMap<String, String>,
}

/// 凭据类型枚举
///
/// 支持密码管理器中的各种凭据类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CredentialType {
    /// 登录凭据（用户名/密码）
    Login,
    /// 双因素认证
    TwoFactor,
    /// Passkey/WebAuthn凭据
    Passkey,
    /// 数字钱包
    DigitalWallet,
    /// 服务器凭据
    Server,
    /// 安全笔记
    SecureNote,
    /// 身份信息
    Identity,
    /// 支付卡信息
    PaymentCard,
    /// 文档附件
    Document,
    /// 自定义类型
    Custom(String),
}

/// 登录凭据数据结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct LoginCredential {
    /// 网站名称或应用名称
    pub name: String,
    /// 网站URL
    pub url: Option<String>,
    /// 用户名
    pub username: String,
    /// 密码
    pub password: String,
    /// 备注
    pub notes: Option<String>,
    /// 标签列表
    pub tags: Vec<String>,
    /// 所属文件夹ID
    pub folder_id: Option<String>,
    /// 是否为收藏
    pub favorite: bool,
    /// 自定义字段
    pub custom_fields: HashMap<String, String>,
    /// 密码历史记录
    pub password_history: Vec<PasswordHistoryEntry>,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
}

/// 双因素认证凭据数据结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct TwoFactorCredential {
    /// 关联的登录凭据ID
    pub login_credential_id: String,
    /// 2FA类型
    pub two_factor_type: TwoFactorType,
    /// TOTP密钥（Base32编码）
    pub secret: Option<String>,
    /// 备份码列表
    pub backup_codes: Vec<String>,
    /// 恢复码
    pub recovery_codes: Vec<String>,
    /// 发行者名称
    pub issuer: Option<String>,
    /// 账户名称
    pub account_name: Option<String>,
    /// TOTP算法
    pub algorithm: Option<String>,
    /// TOTP位数
    pub digits: Option<u32>,
    /// TOTP时间步长（秒）
    pub period: Option<u32>,
}

/// 双因素认证类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum TwoFactorType {
    /// 基于时间的一次性密码
    Totp,
    /// 基于计数器的一次性密码
    Hotp,
    /// SMS短信验证
    Sms,
    /// 邮件验证
    Email,
    /// 硬件令牌
    Hardware,
    /// 推送通知
    Push,
}

/// Passkey凭据数据结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PasskeyCredential {
    /// 关联的登录凭据ID
    pub login_credential_id: Option<String>,
    /// 凭据ID（Base64编码）
    pub credential_id: String,
    /// 公钥（Base64编码）
    pub public_key: String,
    /// 私钥（加密存储）
    pub private_key: String,
    /// 依赖方ID
    pub rp_id: String,
    /// 依赖方名称
    pub rp_name: String,
    /// 用户句柄
    pub user_handle: String,
    /// 用户名
    pub username: String,
    /// 用户显示名称
    pub user_display_name: String,
    /// 签名计数器
    pub signature_counter: u32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
}

/// 数字钱包凭据数据结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DigitalWalletCredential {
    /// 钱包名称
    pub name: String,
    /// 钱包类型
    pub wallet_type: WalletType,
    /// 钱包地址
    pub address: String,
    /// 私钥（加密存储）
    pub private_key: Option<String>,
    /// 助记词（加密存储）
    pub mnemonic: Option<String>,
    /// 密码/PIN
    pub password: Option<String>,
    /// 网络/链信息
    pub network: String,
    /// 余额信息
    pub balance: Option<String>,
    /// 备注
    pub notes: Option<String>,
    /// 标签
    pub tags: Vec<String>,
}

/// 数字钱包类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum WalletType {
    /// 比特币钱包
    Bitcoin,
    /// 以太坊钱包
    Ethereum,
    /// 其他加密货币钱包
    Cryptocurrency,
    /// 硬件钱包
    Hardware,
    /// 软件钱包
    Software,
    /// 纸钱包
    Paper,
}

/// 服务器凭据数据结构
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ServerCredential {
    /// 服务器名称
    pub name: String,
    /// 服务器地址
    pub hostname: String,
    /// 端口号
    pub port: Option<u16>,
    /// 协议类型
    pub protocol: String,
    /// 用户名
    pub username: String,
    /// 密码
    pub password: Option<String>,
    /// SSH私钥
    pub ssh_private_key: Option<String>,
    /// SSH公钥
    pub ssh_public_key: Option<String>,
    /// 备注
    pub notes: Option<String>,
    /// 标签
    pub tags: Vec<String>,
}

/// 密码历史记录条目
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PasswordHistoryEntry {
    /// 密码值
    pub password: String,
    /// 使用时间
    pub used_at: DateTime<Utc>,
    /// 密码强度评分
    pub strength_score: Option<f64>,
}

/// 同步记录构建器
///
/// 提供便捷的方式创建同步记录
#[derive(Debug)]
pub struct SyncRecordBuilder {
    credential_id: String,
    credential_type: CredentialType,
    operation_type: OperationType,
    device_id: String,
    data: Option<String>,
    timestamp: Option<DateTime<Utc>>,
    version: Option<i64>,
    vector_clock: Option<VectorClock>,
    checksum: Option<String>,
    metadata: HashMap<String, String>,
}

impl SyncRecord {
    /// 创建新的同步记录
    pub fn new(
        credential_id: String,
        credential_type: CredentialType,
        operation_type: OperationType,
        device_id: String,
    ) -> Self {
        let now = Utc::now();
        let id = Uuid::new_v4();

        Self {
            id,
            credential_id,
            credential_type,
            operation_type,
            data: None,
            data_hash: None,
            version: generate_version() as i64,
            device_id,
            local_timestamp: now,
            server_timestamp: None,
            vector_clock: None,
            causal_dependencies: Vec::new(),
            synced: false,
            retry_count: 0,
            created_at: now,
            updated_at: now,
            metadata: HashMap::new(),
        }
    }

    /// 创建构建器
    pub fn builder(
        credential_id: String,
        credential_type: CredentialType,
        operation_type: OperationType,
        device_id: String,
    ) -> SyncRecordBuilder {
        SyncRecordBuilder {
            credential_id,
            credential_type,
            operation_type,
            device_id,
            data: None,
            timestamp: None,
            version: None,
            vector_clock: None,
            checksum: None,
            metadata: HashMap::new(),
        }
    }

    /// 设置数据并自动计算哈希值
    pub fn set_data(&mut self, data: String) {
        self.data_hash = Some(calculate_data_hash(&data));
        self.data = Some(data);
        self.updated_at = Utc::now();
    }

    /// 设置向量时钟
    pub fn set_vector_clock(&mut self, vector_clock: VectorClock) {
        self.vector_clock = Some(vector_clock);
        self.updated_at = Utc::now();
    }

    /// 添加因果依赖
    pub fn add_causal_dependency(&mut self, dependency_id: Uuid) {
        if !self.causal_dependencies.contains(&dependency_id) {
            self.causal_dependencies.push(dependency_id);
            self.updated_at = Utc::now();
        }
    }

    /// 设置服务端时间戳
    pub fn set_server_timestamp(&mut self, timestamp: DateTime<Utc>) {
        self.server_timestamp = Some(timestamp);
        self.updated_at = Utc::now();
    }

    /// 标记为已同步
    pub fn mark_synced(&mut self) {
        self.synced = true;
        self.updated_at = Utc::now();
    }

    /// 增加重试次数
    pub fn increment_retry_count(&mut self) {
        self.retry_count += 1;
        self.updated_at = Utc::now();
    }

    /// 添加元数据
    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
        self.updated_at = Utc::now();
    }

    /// 检查是否需要同步
    pub fn needs_sync(&self) -> bool {
        !self.synced
    }

    /// 检查是否可以重试
    pub fn can_retry(&self, max_retries: u32) -> bool {
        self.retry_count < max_retries
    }

    /// 获取数据的JSON值
    pub fn get_data_json(&self) -> Result<Option<serde_json::Value>, serde_json::Error> {
        match &self.data {
            Some(data) => Ok(Some(serde_json::from_str(data)?)),
            None => Ok(None),
        }
    }

    /// 验证数据完整性
    pub fn validate_integrity(&self) -> bool {
        match (&self.data, &self.data_hash) {
            (Some(data), Some(hash)) => {
                let calculated_hash = calculate_data_hash(data);
                calculated_hash == *hash
            }
            (None, None) => true,
            _ => false,
        }
    }
}

impl SyncRecordBuilder {
    /// 设置数据
    pub fn data(mut self, data: String) -> Self {
        self.data = Some(data);
        self
    }

    /// 设置时间戳
    pub fn timestamp(mut self, timestamp: DateTime<Utc>) -> Self {
        self.timestamp = Some(timestamp);
        self
    }

    /// 设置版本
    pub fn version(mut self, version: i64) -> Self {
        self.version = Some(version);
        self
    }

    /// 设置向量时钟
    pub fn vector_clock(mut self, vector_clock: VectorClock) -> Self {
        self.vector_clock = Some(vector_clock);
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    /// 构建同步记录
    pub fn build(self) -> Result<SyncRecord, SyncError> {
        let now = Utc::now();
        let data_hash = self.data.as_ref().map(|d| calculate_data_hash(d));

        Ok(SyncRecord {
            id: Uuid::new_v4(),
            credential_id: self.credential_id,
            credential_type: self.credential_type,
            operation_type: self.operation_type,
            data: self.data,
            data_hash,
            version: generate_version() as i64,
            device_id: self.device_id,
            local_timestamp: self.timestamp.unwrap_or(now),
            server_timestamp: None,
            vector_clock: self.vector_clock,
            causal_dependencies: Vec::new(),
            synced: false,
            retry_count: 0,
            created_at: now,
            updated_at: now,
            metadata: self.metadata,
        })
    }
}

impl CredentialType {
    /// 获取所有支持的凭据类型
    pub fn all() -> Vec<CredentialType> {
        vec![
            CredentialType::Login,
            CredentialType::TwoFactor,
            CredentialType::Passkey,
            CredentialType::DigitalWallet,
            CredentialType::Server,
            CredentialType::SecureNote,
            CredentialType::Identity,
            CredentialType::PaymentCard,
            CredentialType::Document,
        ]
    }

    /// 获取类型名称
    pub fn name(&self) -> String {
        match self {
            CredentialType::Login => "login".to_string(),
            CredentialType::TwoFactor => "two_factor".to_string(),
            CredentialType::Passkey => "passkey".to_string(),
            CredentialType::DigitalWallet => "digital_wallet".to_string(),
            CredentialType::Server => "server".to_string(),
            CredentialType::SecureNote => "secure_note".to_string(),
            CredentialType::Identity => "identity".to_string(),
            CredentialType::PaymentCard => "payment_card".to_string(),
            CredentialType::Document => "document".to_string(),
            CredentialType::Custom(name) => name.clone(),
        }
    }

    /// 从字符串解析凭据类型
    pub fn from_str(s: &str) -> Self {
        match s {
            "login" => CredentialType::Login,
            "two_factor" => CredentialType::TwoFactor,
            "passkey" => CredentialType::Passkey,
            "digital_wallet" => CredentialType::DigitalWallet,
            "server" => CredentialType::Server,
            "secure_note" => CredentialType::SecureNote,
            "identity" => CredentialType::Identity,
            "payment_card" => CredentialType::PaymentCard,
            "document" => CredentialType::Document,
            _ => CredentialType::Custom(s.to_string()),
        }
    }

    /// 检查是否支持向量时钟
    pub fn supports_vector_clock(&self) -> bool {
        // 所有类型都支持向量时钟
        true
    }

    /// 检查是否支持三路合并
    pub fn supports_three_way_merge(&self) -> bool {
        match self {
            CredentialType::Login => true,
            CredentialType::TwoFactor => true,
            CredentialType::Passkey => false, // Passkey通常不需要合并
            CredentialType::DigitalWallet => true,
            CredentialType::Server => true,
            CredentialType::SecureNote => true,
            CredentialType::Identity => true,
            CredentialType::PaymentCard => true,
            CredentialType::Document => false, // 文档通常不合并
            CredentialType::Custom(_) => true,
        }
    }

    /// 检查是否为内置类型
    pub fn is_builtin(&self) -> bool {
        !matches!(self, CredentialType::Custom(_))
    }
}

impl Default for LoginCredential {
    fn default() -> Self {
        Self {
            name: String::new(),
            url: None,
            username: String::new(),
            password: String::new(),
            notes: None,
            tags: Vec::new(),
            folder_id: None,
            favorite: false,
            custom_fields: HashMap::new(),
            password_history: Vec::new(),
            last_used: None,
        }
    }
}

impl Default for TwoFactorCredential {
    fn default() -> Self {
        Self {
            login_credential_id: String::new(),
            two_factor_type: TwoFactorType::Totp,
            secret: None,
            backup_codes: Vec::new(),
            recovery_codes: Vec::new(),
            issuer: None,
            account_name: None,
            algorithm: Some("SHA1".to_string()),
            digits: Some(6),
            period: Some(30),
        }
    }
}

impl Default for PasskeyCredential {
    fn default() -> Self {
        Self {
            login_credential_id: None,
            credential_id: String::new(),
            public_key: String::new(),
            private_key: String::new(),
            rp_id: String::new(),
            rp_name: String::new(),
            user_handle: String::new(),
            username: String::new(),
            user_display_name: String::new(),
            signature_counter: 0,
            created_at: Utc::now(),
            last_used: None,
        }
    }
}

impl Default for DigitalWalletCredential {
    fn default() -> Self {
        Self {
            name: String::new(),
            wallet_type: WalletType::Software,
            address: String::new(),
            private_key: None,
            mnemonic: None,
            password: None,
            network: String::new(),
            balance: None,
            notes: None,
            tags: Vec::new(),
        }
    }
}

impl Default for ServerCredential {
    fn default() -> Self {
        Self {
            name: String::new(),
            hostname: String::new(),
            port: None,
            protocol: "ssh".to_string(),
            username: String::new(),
            password: None,
            ssh_private_key: None,
            ssh_public_key: None,
            notes: None,
            tags: Vec::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sync_record_creation() {
        let record = SyncRecord::new(
            "cred_123".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_1".to_string(),
        );

        assert_eq!(record.credential_id, "cred_123");
        assert_eq!(record.credential_type, CredentialType::Login);
        assert_eq!(record.operation_type, OperationType::Create);
        assert_eq!(record.device_id, "device_1");
        assert!(!record.synced);
        assert_eq!(record.retry_count, 0);
        assert!(record.needs_sync());
    }

    #[test]
    fn test_sync_record_builder() {
        let data = r#"{"username": "test", "password": "secret"}"#;
        let record = SyncRecord::builder(
            "cred_123".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_001".to_string(),
        )
        .data(data.to_string())
        .with_metadata("source".to_string(), "manual".to_string())
        .build()
        .unwrap();

        assert_eq!(record.data, Some(data.to_string()));
        assert!(record.data_hash.is_some());
        assert_eq!(record.metadata.get("source"), Some(&"manual".to_string()));
    }

    #[test]
    fn test_data_integrity_validation() {
        let mut record = SyncRecord::new(
            "cred_123".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_1".to_string(),
        );

        // 没有数据时应该验证通过
        assert!(record.validate_integrity());

        // 设置数据后应该验证通过
        record.set_data(r#"{"test": "data"}"#.to_string());
        assert!(record.validate_integrity());

        // 手动修改哈希值后应该验证失败
        record.data_hash = Some("invalid_hash".to_string());
        assert!(!record.validate_integrity());
    }

    #[test]
    fn test_credential_type_operations() {
        assert_eq!(CredentialType::Login.name(), "login");
        assert_eq!(CredentialType::TwoFactor.name(), "two_factor");

        assert_eq!(CredentialType::from_str("login"), CredentialType::Login);
        assert_eq!(
            CredentialType::from_str("unknown"),
            CredentialType::Custom("unknown".to_string())
        );

        assert!(CredentialType::Login.supports_vector_clock());
        assert!(CredentialType::Login.supports_three_way_merge());
        assert!(!CredentialType::Passkey.supports_three_way_merge());
    }

    #[test]
    fn test_sync_record_operations() {
        let mut record = SyncRecord::new(
            "cred_123".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_1".to_string(),
        );

        // 测试重试逻辑
        assert!(record.can_retry(3));
        record.increment_retry_count();
        assert_eq!(record.retry_count, 1);

        record.increment_retry_count();
        record.increment_retry_count();
        record.increment_retry_count();
        assert!(!record.can_retry(3));

        // 测试同步状态
        assert!(record.needs_sync());
        record.mark_synced();
        assert!(!record.needs_sync());
    }

    #[test]
    fn test_login_credential_default() {
        let cred = LoginCredential::default();
        assert_eq!(cred.name, "");
        assert_eq!(cred.username, "");
        assert_eq!(cred.password, "");
        assert!(!cred.favorite);
        assert!(cred.tags.is_empty());
        assert!(cred.custom_fields.is_empty());
    }

    #[test]
    fn test_two_factor_credential_default() {
        let cred = TwoFactorCredential::default();
        assert_eq!(cred.two_factor_type, TwoFactorType::Totp);
        assert_eq!(cred.algorithm, Some("SHA1".to_string()));
        assert_eq!(cred.digits, Some(6));
        assert_eq!(cred.period, Some(30));
    }

    #[test]
    fn test_json_serialization() {
        let record = SyncRecord::builder(
            "cred_123".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_1".to_string(),
        )
        .data(r#"{"username": "test"}"#.to_string())
        .build()
        .unwrap();

        // 测试序列化
        let json = serde_json::to_string(&record).unwrap();
        assert!(json.contains("cred_123"));

        // 测试反序列化
        let deserialized: SyncRecord = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.credential_id, record.credential_id);
        assert_eq!(deserialized.credential_type, record.credential_type);
    }

    #[test]
    fn test_get_data_json() {
        let mut record = SyncRecord::new(
            "cred_123".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_1".to_string(),
        );

        // 没有数据时返回None
        assert_eq!(record.get_data_json().unwrap(), None);

        // 有效JSON数据
        record.set_data(r#"{"username": "test", "password": "secret"}"#.to_string());
        let json_value = record.get_data_json().unwrap().unwrap();
        assert_eq!(json_value["username"], "test");
        assert_eq!(json_value["password"], "secret");

        // 无效JSON数据
        record.data = Some("invalid json".to_string());
        assert!(record.get_data_json().is_err());
    }
}
