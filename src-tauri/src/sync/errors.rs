//! 同步系统错误类型定义
//!
//! 使用 thiserror 提供精确的错误类型层次结构

use std::fmt;
use thiserror::Error;

/// 同步系统主错误类型
#[derive(Error, Debug)]
pub enum SyncError {
    /// 向量时钟相关错误
    #[error("向量时钟操作失败: {operation}")]
    VectorClock {
        operation: String,
        #[source]
        source: Box<dyn std::error::Error + Send + Sync>,
    },

    /// NTP时间同步错误
    #[error("NTP时间同步失败: {operation}")]
    Ntp {
        operation: String,
        #[source]
        source: Box<dyn std::error::Error + Send + Sync>,
    },

    /// 存储操作错误
    #[error("存储操作失败: {operation}")]
    Storage {
        operation: String,
        #[source]
        source: Box<dyn std::error::Error + Send + Sync>,
    },

    /// 网络请求错误
    #[error("网络请求失败: {url}")]
    Network {
        url: String,
        status_code: Option<u16>,
        #[source]
        source: Box<dyn std::error::Error + Send + Sync>,
    },

    /// 序列化/反序列化错误
    #[error("序列化失败: {operation}")]
    Serialization {
        operation: String,
        data_type: String,
    },

    /// 冲突解决错误
    #[error("冲突解决失败: 冲突数量 {conflict_count}")]
    ConflictResolution {
        conflict_count: usize,
        conflict_type: String,
        credential_id: Option<String>,
    },

    /// 配置错误
    #[error("配置错误: {field} - {message}")]
    Configuration { field: String, message: String },

    /// 资源未找到错误
    #[error("资源未找到: {resource_type} '{resource_id}'")]
    ResourceNotFound {
        resource_type: String,
        resource_id: String,
    },

    /// 权限错误
    #[error("权限不足: {operation}")]
    Permission {
        operation: String,
        required_permission: String,
    },

    /// 验证错误
    #[error("验证失败: {field} - {message}")]
    Validation { field: String, message: String },

    /// 超时错误
    #[error("操作超时: {operation} (超时时间: {timeout_ms}ms)")]
    Timeout { operation: String, timeout_ms: u64 },

    /// 内部错误
    #[error("内部错误: {message}")]
    Internal {
        message: String,
        error_code: Option<String>,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    /// 反序列化错误
    #[error("反序列化失败: {operation}")]
    Deserialization {
        operation: String,
        data_type: String,
    },
}

/// 向量时钟错误类型
#[derive(Error, Debug)]
pub enum VectorClockError {
    /// 设备ID为空
    #[error("设备ID不能为空")]
    EmptyDeviceId,

    /// 时钟值溢出
    #[error("时钟值溢出: 设备 {device_id}, 当前值 {current_value}")]
    ClockOverflow {
        device_id: String,
        current_value: u64,
    },

    /// 向量时钟大小超限
    #[error("向量时钟大小超限: 当前 {current_size}, 最大 {max_size}")]
    SizeExceeded {
        current_size: usize,
        max_size: usize,
    },

    /// 无效的设备ID
    #[error("无效的设备ID: {device_id}")]
    InvalidDeviceId { device_id: String },

    /// 序列化错误
    #[error("向量时钟序列化失败: {message}")]
    SerializationError { message: String },

    /// 反序列化错误
    #[error("向量时钟反序列化失败: {message}")]
    DeserializationError { message: String },
}

/// NTP错误类型
#[derive(Error, Debug)]
pub enum NtpError {
    /// 服务器连接失败
    #[error("NTP服务器连接失败: {server}")]
    ServerConnectionFailed { server: String },

    /// 响应超时
    #[error("NTP请求超时: {server} (超时时间: {timeout_ms}ms)")]
    RequestTimeout { server: String, timeout_ms: u64 },

    /// 响应解析失败
    #[error("NTP响应解析失败: {reason}")]
    ResponseParseFailed { reason: String },

    /// 时间偏差过大
    #[error("时间偏差过大: {offset_ms}ms > {max_offset_ms}ms")]
    TimeOffsetTooLarge { offset_ms: i64, max_offset_ms: i64 },

    /// 没有可用的时间源
    #[error("没有可用的时间源")]
    NoTimeSourceAvailable,
}

/// 存储错误类型
#[derive(Error, Debug)]
pub enum StorageError {
    /// 数据库连接失败
    #[error("数据库连接失败: {database_url}")]
    DatabaseConnectionFailed { database_url: String },

    /// 记录未找到
    #[error("记录未找到: {record_id}")]
    RecordNotFound { record_id: String },

    /// 记录已存在
    #[error("记录已存在: {record_id}")]
    RecordAlreadyExists { record_id: String },

    /// 查询执行失败
    #[error("查询执行失败: {query}")]
    QueryExecutionFailed { query: String },

    /// 事务失败
    #[error("事务失败: {operation}")]
    TransactionFailed { operation: String },

    /// 存储空间不足
    #[error("存储空间不足: 需要 {required_bytes} 字节, 可用 {available_bytes} 字节")]
    InsufficientStorage {
        required_bytes: u64,
        available_bytes: u64,
    },

    /// 数据损坏
    #[error("数据损坏: {record_id} - {reason}")]
    DataCorruption { record_id: String, reason: String },
}

/// 合并错误类型
#[derive(Error, Debug)]
pub enum MergeError {
    /// 冲突检测失败
    #[error("冲突检测失败: {reason}")]
    ConflictDetectionFailed { reason: String },

    /// 合并策略不支持
    #[error("不支持的合并策略: {strategy}")]
    UnsupportedStrategy { strategy: String },

    /// 数据类型不匹配
    #[error("数据类型不匹配: 期望 {expected}, 实际 {actual}")]
    DataTypeMismatch { expected: String, actual: String },

    /// 合并结果无效
    #[error("合并结果无效: {reason}")]
    InvalidMergeResult { reason: String },

    /// 循环依赖
    #[error("检测到循环依赖: {dependency_chain}")]
    CircularDependency { dependency_chain: String },
}

/// 网络错误类型
#[derive(Error, Debug)]
pub enum NetworkError {
    /// 连接超时
    #[error("连接超时: {endpoint} (超时时间: {timeout_ms}ms)")]
    ConnectionTimeout { endpoint: String, timeout_ms: u64 },

    /// DNS解析失败
    #[error("DNS解析失败: {hostname}")]
    DnsResolutionFailed { hostname: String },

    /// SSL/TLS错误
    #[error("SSL/TLS错误: {endpoint} - {reason}")]
    SslError { endpoint: String, reason: String },

    /// HTTP错误
    #[error("HTTP错误: {status_code} - {message}")]
    HttpError { status_code: u16, message: String },

    /// 网络不可达
    #[error("网络不可达: {endpoint}")]
    NetworkUnreachable { endpoint: String },

    /// 请求被拒绝
    #[error("请求被拒绝: {endpoint} - {reason}")]
    RequestRejected { endpoint: String, reason: String },
}

// 错误转换实现
impl From<VectorClockError> for SyncError {
    fn from(err: VectorClockError) -> Self {
        SyncError::VectorClock {
            operation: "vector_clock_operation".to_string(),
            source: Box::new(err),
        }
    }
}

impl From<NtpError> for SyncError {
    fn from(err: NtpError) -> Self {
        SyncError::Ntp {
            operation: "ntp_operation".to_string(),
            source: Box::new(err),
        }
    }
}

impl From<StorageError> for SyncError {
    fn from(err: StorageError) -> Self {
        SyncError::Storage {
            operation: "storage_operation".to_string(),
            source: Box::new(err),
        }
    }
}

impl From<MergeError> for SyncError {
    fn from(err: MergeError) -> Self {
        SyncError::ConflictResolution {
            conflict_count: 1,
            conflict_type: "unknown".to_string(),
            credential_id: None,
        }
    }
}

impl From<NetworkError> for SyncError {
    fn from(err: NetworkError) -> Self {
        match err {
            NetworkError::ConnectionTimeout {
                endpoint,
                timeout_ms,
            } => SyncError::Network {
                url: endpoint,
                status_code: None,
                source: Box::new(NetworkError::ConnectionTimeout {
                    endpoint: "".to_string(),
                    timeout_ms,
                }),
            },
            NetworkError::HttpError {
                status_code,
                message,
            } => SyncError::Network {
                url: "unknown".to_string(),
                status_code: Some(status_code),
                source: Box::new(NetworkError::HttpError {
                    status_code,
                    message,
                }),
            },
            _ => SyncError::Network {
                url: "unknown".to_string(),
                status_code: None,
                source: Box::new(err),
            },
        }
    }
}

impl From<serde_json::Error> for SyncError {
    fn from(err: serde_json::Error) -> Self {
        SyncError::Serialization {
            operation: "json_serialization".to_string(),
            data_type: "unknown".to_string(),
        }
    }
}

impl From<tokio::time::error::Elapsed> for SyncError {
    fn from(_err: tokio::time::error::Elapsed) -> Self {
        SyncError::Timeout {
            operation: "async_operation".to_string(),
            timeout_ms: 0,
        }
    }
}

impl From<std::io::Error> for SyncError {
    fn from(err: std::io::Error) -> Self {
        SyncError::Internal {
            message: "IO操作失败".to_string(),
            error_code: Some(format!("{:?}", err.kind())),
            source: Some(Box::new(err)),
        }
    }
}

// 便捷的错误创建函数
impl SyncError {
    /// 创建向量时钟错误
    pub fn vector_clock_error(
        operation: &str,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        SyncError::VectorClock {
            operation: operation.to_string(),
            source: Box::new(source),
        }
    }

    /// 创建NTP错误
    pub fn ntp_error(
        operation: &str,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        SyncError::Ntp {
            operation: operation.to_string(),
            source: Box::new(source),
        }
    }

    /// 创建存储错误
    pub fn storage_error(
        operation: &str,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        SyncError::Storage {
            operation: operation.to_string(),
            source: Box::new(source),
        }
    }

    /// 创建网络错误
    pub fn network_error(
        url: &str,
        source: impl std::error::Error + Send + Sync + 'static,
    ) -> Self {
        SyncError::Network {
            url: url.to_string(),
            status_code: None,
            source: Box::new(source),
        }
    }

    /// 创建配置错误
    pub fn configuration_error(field: &str, message: &str) -> Self {
        SyncError::Configuration {
            field: field.to_string(),
            message: message.to_string(),
        }
    }

    /// 创建验证错误
    pub fn validation_error(field: &str, message: &str) -> Self {
        SyncError::Validation {
            field: field.to_string(),
            message: message.to_string(),
        }
    }

    /// 创建资源未找到错误
    pub fn resource_not_found(resource_type: &str, resource_id: &str) -> Self {
        SyncError::ResourceNotFound {
            resource_type: resource_type.to_string(),
            resource_id: resource_id.to_string(),
        }
    }

    /// 创建内部错误
    pub fn internal_error(message: &str) -> Self {
        SyncError::Internal {
            message: message.to_string(),
            error_code: None,
            source: None,
        }
    }

    /// 创建超时错误
    pub fn timeout_error(operation: &str, timeout_ms: u64) -> Self {
        SyncError::Timeout {
            operation: operation.to_string(),
            timeout_ms,
        }
    }

    /// 获取错误代码
    pub fn error_code(&self) -> String {
        match self {
            SyncError::VectorClock { .. } => "VECTOR_CLOCK_ERROR".to_string(),
            SyncError::Ntp { .. } => "NTP_ERROR".to_string(),
            SyncError::Storage { .. } => "STORAGE_ERROR".to_string(),
            SyncError::Network { .. } => "NETWORK_ERROR".to_string(),
            SyncError::Serialization { .. } => "SERIALIZATION_ERROR".to_string(),
            SyncError::ConflictResolution { .. } => "CONFLICT_RESOLUTION_ERROR".to_string(),
            SyncError::Configuration { .. } => "CONFIGURATION_ERROR".to_string(),
            SyncError::ResourceNotFound { .. } => "RESOURCE_NOT_FOUND".to_string(),
            SyncError::Permission { .. } => "PERMISSION_ERROR".to_string(),
            SyncError::Validation { .. } => "VALIDATION_ERROR".to_string(),
            SyncError::Timeout { .. } => "TIMEOUT_ERROR".to_string(),
            SyncError::Internal { error_code, .. } => error_code
                .clone()
                .unwrap_or_else(|| "INTERNAL_ERROR".to_string()),
            SyncError::Deserialization { .. } => "DESERIALIZATION_ERROR".to_string(),
        }
    }

    /// 检查是否为可重试的错误
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            SyncError::Network { .. }
                | SyncError::Ntp { .. }
                | SyncError::Timeout { .. }
                | SyncError::Storage { .. }
        )
    }

    /// 检查是否为致命错误
    pub fn is_fatal(&self) -> bool {
        matches!(
            self,
            SyncError::Configuration { .. }
                | SyncError::Permission { .. }
                | SyncError::Validation { .. }
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::error::Error;

    #[test]
    fn test_sync_error_creation() {
        let error = SyncError::configuration_error("device_id", "设备ID不能为空");
        assert_eq!(error.error_code(), "CONFIGURATION_ERROR");
        assert!(error.is_fatal());
        assert!(!error.is_retryable());
    }

    #[test]
    fn test_vector_clock_error() {
        let vc_error = VectorClockError::InvalidDeviceId {
            device_id: "invalid".to_string(),
        };
        let sync_error: SyncError = vc_error.into();
        assert_eq!(sync_error.error_code(), "VECTOR_CLOCK_ERROR");
    }

    #[test]
    fn test_ntp_error() {
        let ntp_error = NtpError::ServerConnectionFailed {
            server: "time.example.com".to_string(),
        };
        let sync_error: SyncError = ntp_error.into();
        assert_eq!(sync_error.error_code(), "NTP_ERROR");
        assert!(sync_error.is_retryable());
    }

    #[test]
    fn test_storage_error() {
        let storage_error = StorageError::RecordNotFound {
            record_id: "test_record".to_string(),
        };
        let sync_error: SyncError = storage_error.into();
        assert_eq!(sync_error.error_code(), "STORAGE_ERROR");
    }

    #[test]
    fn test_network_error() {
        let network_error = NetworkError::ConnectionTimeout {
            endpoint: "https://api.example.com".to_string(),
            timeout_ms: 5000,
        };
        let sync_error: SyncError = network_error.into();
        assert_eq!(sync_error.error_code(), "NETWORK_ERROR");
        assert!(sync_error.is_retryable());
    }

    #[test]
    fn test_merge_error() {
        let merge_error = MergeError::ConflictDetectionFailed {
            reason: "数据类型不匹配".to_string(),
        };
        let sync_error: SyncError = merge_error.into();
        assert_eq!(sync_error.error_code(), "CONFLICT_RESOLUTION_ERROR");
    }

    #[test]
    fn test_error_display() {
        let error = SyncError::ResourceNotFound {
            resource_type: "credential".to_string(),
            resource_id: "test_123".to_string(),
        };
        let error_string = format!("{}", error);
        assert!(error_string.contains("资源未找到"));
        assert!(error_string.contains("credential"));
        assert!(error_string.contains("test_123"));
    }

    #[test]
    fn test_error_chain() {
        let storage_error = StorageError::RecordNotFound {
            record_id: "test_id".to_string(),
        };
        let sync_error = SyncError::Storage {
            operation: "get_record".to_string(),
            source: Box::new(storage_error),
        };

        assert!(sync_error.to_string().contains("存储操作失败"));
        assert!(sync_error.source().is_some());
    }

    #[test]
    fn test_timeout_error() {
        let error = SyncError::timeout_error("数据同步", 30000);
        assert_eq!(error.error_code(), "TIMEOUT_ERROR");
        assert!(error.is_retryable());

        let error_string = format!("{}", error);
        assert!(error_string.contains("操作超时"));
        assert!(error_string.contains("30000ms"));
    }
}
