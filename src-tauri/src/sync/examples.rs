/// 企业级密码管理器同步系统使用示例
/// 
/// 本文件展示了如何使用新实现的同步模块进行多端密码数据同步

use crate::sync::{
    SyncManager, SyncDirection, SyncStrategyConfig,
    SyncRecord, CredentialType, OperationType,
    MergeStrategy, MergeConfig,
    NtpConfig, TimeManager,
    StorageManager, StorageQuery,
    types::{LoginCredential, TwoFactorCredential, PasskeyCredential, DigitalWalletCredential},
};
use std::collections::HashMap;
use chrono::Utc;

/// 基本同步示例
/// 
/// 展示如何创建同步管理器并执行基本的同步操作
pub async fn basic_sync_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 基本同步示例 ===");

    // 1. 创建同步管理器
    let mut sync_manager = SyncManager::with_default_config("device_001".to_string())?;
    
    // 2. 初始化
    sync_manager.initialize().await?;
    
    // 3. 创建一些测试凭据
    let login_record = SyncRecord::builder(
        "login_001".to_string(),
        CredentialType::Login,
        OperationType::Create,
        "device_001".to_string(),
    )
    .data(serde_json::to_string(&LoginCredential {
        name: "Example Login".to_string(),
        username: "<EMAIL>".to_string(),
        password: "secure_password_123".to_string(),
        url: Some("https://example.com".to_string()),
        notes: Some("工作账户".to_string()),
        tags: vec!["work".to_string()],
        folder_id: None,
        favorite: false,
        custom_fields: HashMap::new(),
        password_history: Vec::new(),
        last_used: None,
    })?)
    .build()?;

    let totp_record = SyncRecord::builder(
        "totp_001".to_string(),
        CredentialType::TwoFactor,
        OperationType::Create,
        "device_001".to_string(),
    )
    .data(serde_json::to_string(&TwoFactorCredential {
        login_credential_id: "login_001".to_string(),
        two_factor_type: crate::sync::types::TwoFactorType::Totp,
        secret: Some("JBSWY3DPEHPK3PXP".to_string()),
        backup_codes: vec!["123456".to_string(), "789012".to_string()],
        recovery_codes: vec!["recovery1".to_string()],
        issuer: Some("GitHub".to_string()),
        account_name: Some("<EMAIL>".to_string()),
        algorithm: Some("SHA1".to_string()),
        digits: Some(6),
        period: Some(30),
    })?)
    .build()?;

    // 4. 添加记录到本地
    let login_id = sync_manager.add_record(login_record).await?;
    let totp_id = sync_manager.add_record(totp_record).await?;
    
    println!("已添加登录凭据: {}", login_id);
    println!("已添加2FA凭据: {}", totp_id);

    // 5. 模拟远程设备的记录
    let remote_records = vec![
        SyncRecord::builder(
            "remote_001".to_string(),
            CredentialType::Login,
            OperationType::Create,
            "device_002".to_string(),
        )
        .data(serde_json::to_string(&LoginCredential {
            name: "Company Admin".to_string(),
            username: "<EMAIL>".to_string(),
            password: "admin_password_456".to_string(),
            url: Some("https://company.com/admin".to_string()),
            notes: Some("管理员账户".to_string()),
            tags: vec!["admin".to_string()],
            folder_id: None,
            favorite: false,
            custom_fields: HashMap::new(),
            password_history: Vec::new(),
            last_used: None,
        })?)
        .build()?,
    ];

    // 6. 执行双向同步
    println!("开始双向同步...");
    let sync_result = sync_manager.sync_with_remote(
        remote_records, 
        SyncDirection::Bidirectional
    ).await?;

    println!("同步完成!");
    println!("- 成功: {}", sync_result.success);
    println!("- 同步记录数: {}", sync_result.synced_count);
    println!("- 冲突数: {}", sync_result.conflict_count);
    println!("- 耗时: {}ms", sync_result.duration_ms);

    // 7. 查看所有记录
    let all_records = sync_manager.get_all_records().await?;
    println!("总记录数: {}", all_records.len());

    Ok(())
}

/// 高级同步配置示例
/// 
/// 展示如何配置高级同步选项，包括冲突解决策略、时间同步等
pub async fn advanced_sync_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 高级同步配置示例 ===");

    // 1. 配置NTP时间同步
    let ntp_config = NtpConfig {
        servers: vec![
            crate::sync::ntp::NtpServerConfig {
                host: "time.apple.com".to_string(),
                port: 123,
                timeout_ms: 3000,
                retry_count: 2,
                weight: 1.0,
            },
        ],
        sync_interval_secs: 300,
        max_offset_ms: 500,
        auto_sync: true,
        adjustment_threshold_ms: 50,
    };

    // 2. 配置合并策略
    let merge_config = MergeConfig {
        default_strategy: MergeStrategy::AutoMerge,
        enable_smart_merge: true,
        conflict_threshold_ms: 2000,
        keep_merge_history: true,
        max_merge_depth: 5,
        ..Default::default()
    };

    // 3. 配置同步策略
    let strategy_config = SyncStrategyConfig {
        conflict_resolution: MergeStrategy::VectorClockPriority,
        enable_incremental_sync: true,
        batch_size: 50,
        max_retries: 5,
        retry_interval_secs: 3,
        enable_compression: true,
        enable_encryption: true,
    };

    // 4. 创建带有自定义配置的管理器
    let storage = StorageManager::with_memory_storage();
    let time_manager = TimeManager::new(ntp_config);
    let merge_manager = crate::sync::MergeManager::with_smart_merger(merge_config);

    let config = crate::sync::SyncConfig {
        device_id: "advanced_device".to_string(),
        auto_sync_enabled: true,
        sync_interval_seconds: 120,
        ..Default::default()
    };

    let mut sync_manager = SyncManager::new(config, storage, time_manager, merge_manager);
    sync_manager.update_strategy_config(strategy_config);
    
    sync_manager.initialize().await?;

    println!("高级同步管理器已配置完成");
    println!("- 设备ID: {}", sync_manager.get_config().device_id);
    println!("- 自动同步: {}", sync_manager.get_config().auto_sync_enabled);
    println!("- 冲突解决策略: {:?}", sync_manager.get_strategy_config().conflict_resolution);

    Ok(())
}

/// 多种凭据类型同步示例
/// 
/// 展示如何同步不同类型的凭据：登录、2FA、Passkey、数字钱包等
pub async fn multi_credential_sync_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 多种凭据类型同步示例 ===");

    let mut sync_manager = SyncManager::with_default_config("multi_device".to_string())?;
    sync_manager.initialize().await?;

    // 1. 登录凭据
    let login_cred = SyncRecord::builder(
        "login_multi".to_string(),
        CredentialType::Login,
        OperationType::Create,
        "multi_device".to_string(),
    )
    .data(serde_json::to_string(&LoginCredential {
        name: "Multi Platform".to_string(),
        username: "<EMAIL>".to_string(),
        password: "multi_pass_123".to_string(),
        url: Some("https://multi.com".to_string()),
        notes: Some("多平台账户".to_string()),
        tags: vec!["multi".to_string()],
        folder_id: None,
        favorite: false,
        custom_fields: HashMap::new(),
        password_history: Vec::new(),
        last_used: None,
    })?)
    .build()?;

    // 2. 2FA凭据
    let totp_cred = SyncRecord::builder(
        "totp_multi".to_string(),
        CredentialType::TwoFactor,
        OperationType::Create,
        "multi_device".to_string(),
    )
    .data(serde_json::to_string(&TwoFactorCredential {
        login_credential_id: "login_multi".to_string(),
        two_factor_type: crate::sync::types::TwoFactorType::Totp,
        secret: Some("ABCDEFGHIJKLMNOP".to_string()),
        backup_codes: vec!["111111".to_string(), "222222".to_string()],
        recovery_codes: vec!["recovery1".to_string()],
        issuer: Some("Multi Service".to_string()),
        account_name: Some("<EMAIL>".to_string()),
        algorithm: Some("SHA256".to_string()),
        digits: Some(8),
        period: Some(60),
    })?)
    .build()?;

    // 3. Passkey凭据
    let passkey_cred = SyncRecord::builder(
        "passkey_multi".to_string(),
        CredentialType::Passkey,
        OperationType::Create,
        "multi_device".to_string(),
    )
    .data(serde_json::to_string(&PasskeyCredential {
        login_credential_id: Some("login_multi".to_string()),
        credential_id: "cred_id_123".to_string(),
        public_key: "public_key_data".to_string(),
        private_key: "private_key_data".to_string(),
        rp_id: "multi.com".to_string(),
        rp_name: "Multi Platform".to_string(),
        user_handle: "user123".to_string(),
        username: "<EMAIL>".to_string(),
        user_display_name: "Multi User".to_string(),
        signature_counter: 1,
        created_at: chrono::Utc::now(),
        last_used: None,
    })?)
    .build()?;

    // 4. 数字钱包凭据
    let wallet_cred = SyncRecord::builder(
        "wallet_multi".to_string(),
        CredentialType::DigitalWallet,
        OperationType::Create,
        "multi_device".to_string(),
    )
    .data(serde_json::to_string(&DigitalWalletCredential {
        name: "Main Wallet".to_string(),
        wallet_type: crate::sync::types::WalletType::Ethereum,
        address: "0x1234567890abcdef".to_string(),
        private_key: Some("wallet_private_key".to_string()),
        mnemonic: Some("word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12".to_string()),
        password: Some("wallet_password".to_string()),
        network: "mainnet".to_string(),
        balance: Some("1.5 ETH".to_string()),
        notes: Some("主钱包".to_string()),
        tags: vec!["crypto".to_string()],
    })?)
    .build()?;

    // 添加所有凭据
    sync_manager.add_record(login_cred).await?;
    sync_manager.add_record(totp_cred).await?;
    sync_manager.add_record(passkey_cred).await?;
    sync_manager.add_record(wallet_cred).await?;

    println!("已添加4种不同类型的凭据");

    // 查询特定类型的凭据
    let query = StorageQuery {
        credential_types: Some(vec!["Login".to_string(), "TwoFactor".to_string()]),
        limit: Some(10),
        ..Default::default()
    };

    let filtered_records = sync_manager.query_records(&query).await?;
    println!("查询到 {} 条登录和2FA凭据", filtered_records.len());

    // 获取同步统计
    let stats = sync_manager.get_sync_stats().await;
    println!("同步统计: {:?}", stats);

    Ok(())
}

/// 冲突解决示例
/// 
/// 展示如何处理同步过程中的数据冲突
pub async fn conflict_resolution_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 冲突解决示例 ===");

    let mut sync_manager = SyncManager::with_default_config("conflict_device".to_string())?;
    sync_manager.initialize().await?;

    // 1. 创建本地记录
    let local_record = SyncRecord::builder(
        "conflict_test".to_string(),
        CredentialType::Login,
        OperationType::Create,
        "conflict_device".to_string(),
    )
    .data(serde_json::to_string(&LoginCredential {
        name: "Conflict Test".to_string(),
        username: "<EMAIL>".to_string(),
        password: "local_password".to_string(),
        url: Some("https://example.com".to_string()),
        notes: Some("本地修改的备注".to_string()),
        tags: vec!["local".to_string()],
        folder_id: None,
        favorite: false,
        custom_fields: HashMap::new(),
        password_history: Vec::new(),
        last_used: None,
    })?)
    .build()?;

    sync_manager.add_record(local_record).await?;

    // 2. 模拟远程记录（相同ID但不同内容）
    let remote_record = SyncRecord::builder(
        "conflict_test".to_string(),
        CredentialType::Login,
        OperationType::Update,
        "remote_device".to_string(),
    )
    .data(serde_json::to_string(&LoginCredential {
        name: "Conflict Test".to_string(),
        username: "<EMAIL>".to_string(),
        password: "remote_password".to_string(),
        url: Some("https://example.com".to_string()),
        notes: Some("远程修改的备注".to_string()),
        tags: vec!["remote".to_string()],
        folder_id: None,
        favorite: false,
        custom_fields: HashMap::new(),
        password_history: Vec::new(),
        last_used: None,
    })?)
    .build()?;

    // 3. 执行同步，这会产生冲突
    println!("执行可能产生冲突的同步...");
    let sync_result = sync_manager.sync_with_remote(
        vec![remote_record], 
        SyncDirection::Bidirectional
    ).await?;

    println!("同步结果:");
    println!("- 成功: {}", sync_result.success);
    println!("- 冲突数: {}", sync_result.conflict_count);

    // 4. 查看最终合并的结果
    let final_record = sync_manager.get_record("conflict_test").await?;
    if let Some(record) = final_record {
        if let Some(data) = &record.data {
            if let Ok(login) = serde_json::from_str::<LoginCredential>(data) {
                println!("最终合并结果:");
                println!("  - 用户名: {}", login.username);
                println!("  - 备注: {:?}", login.notes);
            }
        }
    }

    Ok(())
}

/// 性能测试示例
/// 
/// 展示同步系统在大量数据下的性能表现
pub async fn performance_test_example() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 性能测试示例 ===");

    let mut sync_manager = SyncManager::with_default_config("perf_device".to_string())?;
    sync_manager.initialize().await?;

    let start_time = std::time::Instant::now();

    // 1. 批量添加记录
    println!("批量添加1000条记录...");
    for i in 0..1000 {
        let record = SyncRecord::builder(
            format!("perf_record_{}", i),
            CredentialType::Login,
            OperationType::Create,
            "perf_device".to_string(),
        )
        .data(serde_json::to_string(&LoginCredential {
            name: format!("Test Site {}", i),
            username: format!("user{}@example.com", i),
            password: format!("password_{}", i),
            url: Some(format!("https://site{}.com", i)),
            notes: Some(format!("测试记录 {}", i)),
            tags: vec!["test".to_string()],
            folder_id: None,
            favorite: false,
            custom_fields: HashMap::new(),
            password_history: Vec::new(),
            last_used: None,
        })?)
        .build()?;

        sync_manager.add_record(record).await?;

        if i % 100 == 0 {
            println!("已添加 {} 条记录", i + 1);
        }
    }

    let add_duration = start_time.elapsed();
    println!("添加1000条记录耗时: {:?}", add_duration);

    // 2. 批量查询测试
    let query_start = std::time::Instant::now();
    let all_records = sync_manager.get_all_records().await?;
    let query_duration = query_start.elapsed();
    
    println!("查询{}条记录耗时: {:?}", all_records.len(), query_duration);

    // 3. 模拟大批量同步
    let sync_start = std::time::Instant::now();
    let remote_records: Vec<_> = (1000..1100).map(|i| {
        SyncRecord::builder(
            format!("remote_record_{}", i),
            CredentialType::Login,
            OperationType::Create,
            "remote_device".to_string(),
        )
        .data(serde_json::to_string(&LoginCredential {
            name: format!("Remote Site {}", i),
            username: format!("remote_user{}@example.com", i),
            password: format!("remote_password_{}", i),
            url: Some(format!("https://remote{}.com", i)),
            notes: Some(format!("远程记录 {}", i)),
            tags: vec!["remote".to_string()],
            folder_id: None,
            favorite: false,
            custom_fields: HashMap::new(),
            password_history: Vec::new(),
            last_used: None,
        }).unwrap())
        .build()
        .unwrap()
    }).collect();

    let sync_result = sync_manager.sync_with_remote(
        remote_records, 
        SyncDirection::Bidirectional
    ).await?;

    let sync_duration = sync_start.elapsed();
    println!("同步100条记录耗时: {:?}", sync_duration);
    println!("同步统计: {:?}", sync_result.statistics);

    Ok(())
}

/// 运行所有示例
pub async fn run_all_examples() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始运行企业级密码管理器同步系统示例\n");

    basic_sync_example().await?;
    println!();

    advanced_sync_example().await?;
    println!();

    multi_credential_sync_example().await?;
    println!();

    conflict_resolution_example().await?;
    println!();

    performance_test_example().await?;
    println!();

    println!("✅ 所有示例运行完成！");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_basic_sync_example() {
        let result = basic_sync_example().await;
        assert!(result.is_ok(), "基本同步示例应该成功运行");
    }

    #[tokio::test]
    async fn test_advanced_sync_example() {
        let result = advanced_sync_example().await;
        assert!(result.is_ok(), "高级同步配置示例应该成功运行");
    }

    #[tokio::test]
    async fn test_multi_credential_sync_example() {
        let result = multi_credential_sync_example().await;
        assert!(result.is_ok(), "多种凭据类型同步示例应该成功运行");
    }

    #[tokio::test]
    async fn test_conflict_resolution_example() {
        let result = conflict_resolution_example().await;
        assert!(result.is_ok(), "冲突解决示例应该成功运行");
    }

    #[tokio::test]
    async fn test_performance_test_example() {
        let result = performance_test_example().await;
        assert!(result.is_ok(), "性能测试示例应该成功运行");
    }
} 