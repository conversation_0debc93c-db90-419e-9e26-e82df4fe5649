//! 同步管理器实现
//!
//! 提供同步系统的统一管理接口

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tokio::time::{interval, Duration};
use uuid;

use crate::sync::types::LoginCredential;
use crate::sync::{
    CredentialType, MergeConfig, MergeManager, MergeOutcome, MergeStrategy, NtpConfig,
    OperationType, StorageManager, StorageQuery, SyncConfig, SyncError, SyncEvent, SyncEventType,
    SyncRecord, SyncResult, SyncStatistics, TimeManager, VectorClock,
};

/// 同步管理器操作结果
pub type ManagerResult<T> = Result<T, SyncError>;

/// 同步会话信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SyncSession {
    /// 会话ID
    pub session_id: String,
    /// 开始时间
    pub start_time: DateTime<Utc>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 本地设备ID
    pub local_device_id: String,
    /// 远程设备ID
    pub remote_device_id: Option<String>,
    /// 同步方向
    pub sync_direction: SyncDirection,
    /// 会话状态
    pub status: SessionStatus,
    /// 处理的记录数
    pub records_processed: usize,
    /// 成功同步的记录数
    pub records_synced: usize,
    /// 冲突数
    pub conflicts_found: usize,
    /// 错误信息
    pub errors: Vec<String>,
}

/// 同步方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SyncDirection {
    /// 双向同步
    Bidirectional,
    /// 仅推送到远程
    PushOnly,
    /// 仅从远程拉取
    PullOnly,
    /// 合并同步
    Merge,
}

/// 会话状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SessionStatus {
    /// 进行中
    InProgress,
    /// 已完成
    Completed,
    /// 失败
    Failed,
    /// 已取消
    Cancelled,
    /// 暂停
    Paused,
}

/// 同步策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStrategyConfig {
    /// 冲突解决策略
    pub conflict_resolution: MergeStrategy,
    /// 是否启用增量同步
    pub enable_incremental_sync: bool,
    /// 批量大小
    pub batch_size: usize,
    /// 重试次数
    pub max_retries: u32,
    /// 重试间隔（秒）
    pub retry_interval_secs: u64,
    /// 是否启用压缩
    pub enable_compression: bool,
    /// 是否启用加密
    pub enable_encryption: bool,
}

impl Default for SyncStrategyConfig {
    fn default() -> Self {
        Self {
            conflict_resolution: MergeStrategy::AutoMerge,
            enable_incremental_sync: true,
            batch_size: 100,
            max_retries: 3,
            retry_interval_secs: 5,
            enable_compression: false,
            enable_encryption: true,
        }
    }
}

/// 同步状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncManagerStatus {
    /// 是否正在同步
    pub is_syncing: bool,
    /// 当前会话
    pub current_session: Option<SyncSession>,
    /// 最后同步时间
    pub last_sync_time: Option<DateTime<Utc>>,
    /// 待同步记录数
    pub pending_records: usize,
    /// 同步统计
    pub statistics: SyncStatistics,
}

/// 同步统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncManagerStats {
    /// 总同步次数
    pub total_syncs: u64,
    /// 成功同步次数
    pub successful_syncs: u64,
    /// 失败同步次数
    pub failed_syncs: u64,
    /// 总处理记录数
    pub total_records_processed: u64,
    /// 总冲突数
    pub total_conflicts: u64,
    /// 平均同步时间（毫秒）
    pub average_sync_time_ms: f64,
    /// 最后同步时间
    pub last_sync_time: Option<DateTime<Utc>>,
}

impl Default for SyncManagerStats {
    fn default() -> Self {
        Self {
            total_syncs: 0,
            successful_syncs: 0,
            failed_syncs: 0,
            total_records_processed: 0,
            total_conflicts: 0,
            average_sync_time_ms: 0.0,
            last_sync_time: None,
        }
    }
}

/// 同步管理器
///
/// 提供同步系统的核心管理功能
pub struct SyncManager {
    /// 配置
    config: SyncConfig,
    /// 存储管理器
    storage: Arc<Mutex<StorageManager>>,
    /// 时间管理器
    time_manager: Arc<Mutex<TimeManager>>,
    /// 合并管理器
    merge_manager: Arc<MergeManager>,
    /// 向量时钟管理器
    vector_clock_manager: Arc<RwLock<crate::sync::vector_clock::VectorClockManager>>,
    /// 当前同步会话
    current_session: Arc<RwLock<Option<SyncSession>>>,
    /// 同步统计
    stats: Arc<RwLock<SyncManagerStats>>,
    /// 事件监听器
    event_listeners: Arc<RwLock<Vec<Box<dyn Fn(SyncEvent) + Send + Sync>>>>,
    /// 自动同步任务句柄
    auto_sync_handle: Arc<Mutex<Option<tokio::task::JoinHandle<()>>>>,
    /// 同步策略配置
    strategy_config: SyncStrategyConfig,
}

impl SyncManager {
    /// 创建新的同步管理器
    ///
    /// # 参数
    /// * `config` - 同步配置
    /// * `storage` - 存储管理器
    /// * `time_manager` - 时间管理器
    /// * `merge_manager` - 合并管理器
    pub fn new(
        config: SyncConfig,
        storage: StorageManager,
        time_manager: TimeManager,
        merge_manager: MergeManager,
    ) -> Self {
        let vector_clock_manager = crate::sync::vector_clock::VectorClockManager::new(
            config.device_id.clone(),
            1000, // max_devices
            500,  // compression_threshold
        );

        Self {
            config,
            storage: Arc::new(Mutex::new(storage)),
            time_manager: Arc::new(Mutex::new(time_manager)),
            merge_manager: Arc::new(merge_manager),
            vector_clock_manager: Arc::new(RwLock::new(vector_clock_manager)),
            current_session: Arc::new(RwLock::new(None)),
            stats: Arc::new(RwLock::new(SyncManagerStats::default())),
            event_listeners: Arc::new(RwLock::new(Vec::new())),
            auto_sync_handle: Arc::new(Mutex::new(None)),
            strategy_config: SyncStrategyConfig::default(),
        }
    }

    /// 使用默认配置创建同步管理器
    ///
    /// # 参数
    /// * `device_id` - 设备ID
    pub fn with_default_config(device_id: String) -> ManagerResult<Self> {
        let config = SyncConfig {
            device_id,
            ..Default::default()
        };

        let storage = StorageManager::with_memory_storage();
        let time_manager = TimeManager::with_system_time();
        let merge_manager = MergeManager::with_smart_merger(Default::default());

        Ok(Self::new(config, storage, time_manager, merge_manager))
    }

    /// 初始化同步管理器
    pub async fn initialize(&mut self) -> ManagerResult<()> {
        // 初始化存储
        self.storage
            .lock()
            .await
            .initialize()
            .await
            .map_err(|e| SyncError::Storage {
                operation: "initialize".to_string(),
                source: Box::new(e),
            })?;

        // 发送初始化事件
        self.emit_event(SyncEvent {
            id: uuid::Uuid::new_v4(),
            event_type: SyncEventType::SyncStarted,
            device_id: self.config.device_id.clone(),
            credential_id: None,
            timestamp: Utc::now(),
            vector_clock: None,
            data: None,
            metadata: HashMap::new(),
        })
        .await;

        Ok(())
    }

    /// 启动自动同步
    pub async fn start_auto_sync(&self) -> ManagerResult<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let mut handle_guard = self.auto_sync_handle.lock().await;
        if handle_guard.is_some() {
            return Err(SyncError::Internal {
                message: "自动同步已经启动".to_string(),
                error_code: None,
                source: None,
            });
        }

        let storage = Arc::clone(&self.storage);
        let time_manager = Arc::clone(&self.time_manager);
        let merge_manager = Arc::clone(&self.merge_manager);
        let vector_clock_manager = Arc::clone(&self.vector_clock_manager);
        let stats = Arc::clone(&self.stats);
        let event_listeners = Arc::clone(&self.event_listeners);
        let device_id = self.config.device_id.clone();
        let sync_interval = self.config.sync_interval_seconds;

        let handle = tokio::spawn(async move {
            let mut interval =
                tokio::time::interval(tokio::time::Duration::from_secs(sync_interval));

            loop {
                interval.tick().await;

                if let Err(e) = Self::perform_auto_sync(
                    &storage,
                    &time_manager,
                    &merge_manager,
                    &vector_clock_manager,
                    &stats,
                    &event_listeners,
                    &device_id,
                )
                .await
                {
                    log::error!("自动同步失败: {}", e);
                }
            }
        });

        *handle_guard = Some(handle);
        Ok(())
    }

    /// 停止自动同步
    pub async fn stop_auto_sync(&self) -> ManagerResult<()> {
        let mut handle_guard = self.auto_sync_handle.lock().await;
        if let Some(handle) = handle_guard.take() {
            handle.abort();
        }
        Ok(())
    }

    /// 与远程同步
    ///
    /// # 参数
    /// * `remote_records` - 远程记录列表
    /// * `direction` - 同步方向
    pub async fn sync_with_remote(
        &self,
        remote_records: Vec<SyncRecord>,
        direction: SyncDirection,
    ) -> ManagerResult<SyncResult> {
        let session_id = self.generate_session_id();
        let start_time = Utc::now();

        // 创建同步会话
        let session = SyncSession {
            session_id: session_id.clone(),
            start_time,
            end_time: None,
            local_device_id: self.config.device_id.clone(),
            remote_device_id: None,
            sync_direction: direction,
            status: SessionStatus::InProgress,
            records_processed: 0,
            records_synced: 0,
            conflicts_found: 0,
            errors: Vec::new(),
        };

        {
            let mut current_session = self.current_session.write().await;
            *current_session = Some(session);
        }

        // 发送同步开始事件
        self.emit_event(SyncEvent {
            id: uuid::Uuid::new_v4(),
            event_type: SyncEventType::SyncStarted,
            device_id: self.config.device_id.clone(),
            credential_id: None,
            timestamp: start_time,
            vector_clock: None,
            data: None,
            metadata: HashMap::new(),
        })
        .await;

        // 执行同步
        let result = match direction {
            SyncDirection::Bidirectional => self.perform_bidirectional_sync(remote_records).await,
            SyncDirection::PushOnly => self.perform_push_sync().await,
            SyncDirection::PullOnly => self.perform_pull_sync(remote_records).await,
            SyncDirection::Merge => self.perform_merge_sync(remote_records).await,
        };

        // 更新会话状态
        {
            let mut current_session = self.current_session.write().await;
            if let Some(ref mut session) = *current_session {
                session.end_time = Some(Utc::now());
                session.status = if result.is_ok() {
                    SessionStatus::Completed
                } else {
                    SessionStatus::Failed
                };
            }
        }

        // 发送同步完成事件
        let event_type = if result.is_ok() {
            SyncEventType::SyncCompleted
        } else {
            SyncEventType::SyncFailed
        };

        self.emit_event(SyncEvent {
            id: uuid::Uuid::new_v4(),
            event_type,
            device_id: self.config.device_id.clone(),
            credential_id: None,
            timestamp: Utc::now(),
            vector_clock: None,
            data: None,
            metadata: HashMap::new(),
        })
        .await;

        result
    }

    /// 执行双向同步
    async fn perform_bidirectional_sync(
        &self,
        remote_records: Vec<SyncRecord>,
    ) -> ManagerResult<SyncResult> {
        let start_time = Utc::now();
        let mut statistics = SyncStatistics::default();

        // 获取本地记录
        let local_records = self
            .storage
            .lock()
            .await
            .get_all_records()
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_all_records".to_string(),
                source: Box::new(e),
            })?;

        // 执行批量合并
        let merge_outcomes = self
            .merge_manager
            .merge_batch(
                &local_records,
                &remote_records,
                Some(&self.strategy_config.conflict_resolution),
            )
            .await?;

        let mut merged_records = Vec::new();
        let mut conflicts = Vec::new();

        for outcome in merge_outcomes {
            if let Some(record) = outcome.merged_record {
                merged_records.push(record);
            }
            conflicts.extend(outcome.conflicts);
        }

        // 更新存储
        for record in &merged_records {
            self.storage
                .lock()
                .await
                .store_record(record)
                .await
                .map_err(|e| SyncError::Storage {
                    operation: "store_record".to_string(),
                    source: Box::new(e),
                })?;
        }

        let end_time = Utc::now();
        statistics.uploaded_records = merged_records.len();
        statistics.downloaded_records = remote_records.len();
        statistics.merged_records = merged_records.len();

        Ok(SyncResult {
            success: true,
            synced_count: merged_records.len(),
            conflict_count: conflicts.len(),
            error: None,
            duration_ms: (end_time.timestamp_millis() - start_time.timestamp_millis()) as u64,
            statistics,
            details: HashMap::new(),
        })
    }

    /// 执行推送同步
    async fn perform_push_sync(&self) -> ManagerResult<SyncResult> {
        let start_time = Utc::now();
        let mut statistics = SyncStatistics::default();

        // 获取需要同步的本地记录
        let local_records = self
            .storage
            .lock()
            .await
            .get_all_records()
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_all_records".to_string(),
                source: Box::new(e),
            })?;

        // 这里应该将记录推送到远程服务器
        // 目前只是模拟推送成功
        statistics.uploaded_records = local_records.len();

        let end_time = Utc::now();

        Ok(SyncResult {
            success: true,
            synced_count: local_records.len(),
            conflict_count: 0,
            error: None,
            duration_ms: (end_time.timestamp_millis() - start_time.timestamp_millis()) as u64,
            statistics,
            details: HashMap::new(),
        })
    }

    /// 执行拉取同步
    async fn perform_pull_sync(
        &self,
        remote_records: Vec<SyncRecord>,
    ) -> ManagerResult<SyncResult> {
        let start_time = Utc::now();
        let mut statistics = SyncStatistics::default();

        // 存储远程记录
        for record in &remote_records {
            self.storage
                .lock()
                .await
                .store_record(record)
                .await
                .map_err(|e| SyncError::Storage {
                    operation: "store_record".to_string(),
                    source: Box::new(e),
                })?;
        }

        statistics.downloaded_records = remote_records.len();

        let end_time = Utc::now();

        Ok(SyncResult {
            success: true,
            synced_count: remote_records.len(),
            conflict_count: 0,
            error: None,
            duration_ms: (end_time.timestamp_millis() - start_time.timestamp_millis()) as u64,
            statistics,
            details: HashMap::new(),
        })
    }

    /// 执行合并同步
    async fn perform_merge_sync(
        &self,
        remote_records: Vec<SyncRecord>,
    ) -> ManagerResult<SyncResult> {
        // 合并同步与双向同步类似
        self.perform_bidirectional_sync(remote_records).await
    }

    /// 添加记录
    pub async fn add_record(&self, mut record: SyncRecord) -> ManagerResult<String> {
        // 更新向量时钟
        {
            let mut vc_manager = self.vector_clock_manager.write().await;
            vc_manager.tick()?;
            record.set_vector_clock(vc_manager.clone_clock());
        }

        // 存储记录
        let record_id = self
            .storage
            .lock()
            .await
            .store_record(&record)
            .await
            .map_err(|e| SyncError::Storage {
                operation: "store_record".to_string(),
                source: Box::new(e),
            })?;

        // 发送事件
        self.emit_event(SyncEvent {
            id: uuid::Uuid::new_v4(),
            event_type: SyncEventType::CredentialChanged,
            device_id: self.config.device_id.clone(),
            credential_id: Some(record.credential_id.clone()),
            timestamp: Utc::now(),
            vector_clock: record.vector_clock.clone(),
            data: None,
            metadata: HashMap::new(),
        })
        .await;

        Ok(record_id)
    }

    /// 更新记录
    pub async fn update_record(
        &self,
        record_id: &str,
        mut updated_record: SyncRecord,
    ) -> ManagerResult<bool> {
        // 获取现有记录
        let existing_record = self
            .storage
            .lock()
            .await
            .get_record(record_id)
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_record".to_string(),
                source: Box::new(e),
            })?;

        if existing_record.is_none() {
            return Err(SyncError::ResourceNotFound {
                resource_type: "record".to_string(),
                resource_id: record_id.to_string(),
            });
        }

        // 更新向量时钟
        {
            let mut vc_manager = self.vector_clock_manager.write().await;
            vc_manager.tick()?;
            updated_record.set_vector_clock(vc_manager.clone_clock());
        }

        // 更新记录
        let success = self
            .storage
            .lock()
            .await
            .update_record(record_id, &updated_record)
            .await
            .map_err(|e| SyncError::Storage {
                operation: "update_record".to_string(),
                source: Box::new(e),
            })?;

        if success {
            // 发送事件
            self.emit_event(SyncEvent {
                id: uuid::Uuid::new_v4(),
                event_type: SyncEventType::CredentialChanged,
                device_id: self.config.device_id.clone(),
                credential_id: Some(updated_record.credential_id.clone()),
                timestamp: Utc::now(),
                vector_clock: updated_record.vector_clock.clone(),
                data: None,
                metadata: HashMap::new(),
            })
            .await;
        }

        Ok(success)
    }

    /// 删除记录
    pub async fn delete_record(&self, record_id: &str) -> ManagerResult<bool> {
        // 获取现有记录
        let existing_record = self
            .storage
            .lock()
            .await
            .get_record(record_id)
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_record".to_string(),
                source: Box::new(e),
            })?;

        if let Some(record) = existing_record {
            // 删除记录
            let success = self
                .storage
                .lock()
                .await
                .delete_record(record_id)
                .await
                .map_err(|e| SyncError::Storage {
                    operation: "delete_record".to_string(),
                    source: Box::new(e),
                })?;

            if success {
                // 发送事件
                self.emit_event(SyncEvent {
                    id: uuid::Uuid::new_v4(),
                    event_type: SyncEventType::CredentialChanged,
                    device_id: self.config.device_id.clone(),
                    credential_id: Some(record.credential_id.clone()),
                    timestamp: Utc::now(),
                    vector_clock: record.vector_clock.clone(),
                    data: None,
                    metadata: HashMap::new(),
                })
                .await;
            }

            Ok(success)
        } else {
            Ok(false)
        }
    }

    /// 获取记录
    pub async fn get_record(&self, record_id: &str) -> ManagerResult<Option<SyncRecord>> {
        self.storage
            .lock()
            .await
            .get_record(record_id)
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_record".to_string(),
                source: Box::new(e),
            })
    }

    /// 查询记录
    pub async fn query_records(&self, query: &StorageQuery) -> ManagerResult<Vec<SyncRecord>> {
        self.storage
            .lock()
            .await
            .get_records(query)
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_records".to_string(),
                source: Box::new(e),
            })
    }

    /// 获取所有记录
    pub async fn get_all_records(&self) -> ManagerResult<Vec<SyncRecord>> {
        self.storage
            .lock()
            .await
            .get_all_records()
            .await
            .map_err(|e| SyncError::Storage {
                operation: "get_all_records".to_string(),
                source: Box::new(e),
            })
    }

    /// 获取同步统计信息
    pub async fn get_sync_stats(&self) -> SyncManagerStats {
        self.stats.read().await.clone()
    }

    /// 获取当前同步会话
    pub async fn get_current_session(&self) -> Option<SyncSession> {
        self.current_session.read().await.clone()
    }

    /// 获取同步状态
    pub async fn get_sync_status(&self) -> SyncManagerStatus {
        let current_session = self.get_current_session().await;
        let is_syncing = current_session
            .as_ref()
            .map(|s| s.status == SessionStatus::InProgress)
            .unwrap_or(false);

        let pending_records = self
            .storage
            .lock()
            .await
            .get_all_records()
            .await
            .map(|records| records.iter().filter(|r| r.needs_sync()).count())
            .unwrap_or(0);

        SyncManagerStatus {
            is_syncing,
            current_session,
            last_sync_time: self.stats.read().await.last_sync_time,
            pending_records,
            statistics: SyncStatistics::default(), // 这里应该从实际统计中获取
        }
    }

    /// 添加事件监听器
    pub async fn add_event_listener<F>(&self, listener: F)
    where
        F: Fn(SyncEvent) + Send + Sync + 'static,
    {
        let mut listeners = self.event_listeners.write().await;
        listeners.push(Box::new(listener));
    }

    /// 发送事件
    async fn emit_event(&self, event: SyncEvent) {
        let listeners = self.event_listeners.read().await;
        for listener in listeners.iter() {
            listener(event.clone());
        }
    }

    /// 生成会话ID
    fn generate_session_id(&self) -> String {
        format!(
            "sync_{}_{}",
            self.config.device_id,
            Utc::now().timestamp_millis()
        )
    }

    /// 执行自动同步
    async fn perform_auto_sync(
        storage: &Arc<Mutex<StorageManager>>,
        time_manager: &Arc<Mutex<TimeManager>>,
        merge_manager: &Arc<MergeManager>,
        vector_clock_manager: &Arc<RwLock<crate::sync::vector_clock::VectorClockManager>>,
        stats: &Arc<RwLock<SyncManagerStats>>,
        event_listeners: &Arc<RwLock<Vec<Box<dyn Fn(SyncEvent) + Send + Sync>>>>,
        device_id: &str,
    ) -> ManagerResult<()> {
        // 这里实现自动同步逻辑
        // 目前只是一个占位符实现

        // 同步时间
        if let Ok(mut tm) = time_manager.try_lock() {
            if let Err(e) = tm.sync_time().await {
                log::warn!("时间同步失败: {}", e);
            }
        }

        // 更新统计信息
        {
            let mut stats_guard = stats.write().await;
            stats_guard.total_syncs += 1;
            stats_guard.last_sync_time = Some(Utc::now());
        }

        Ok(())
    }

    /// 获取配置
    pub fn get_config(&self) -> &SyncConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: SyncConfig) {
        self.config = config;
    }

    /// 获取策略配置
    pub fn get_strategy_config(&self) -> &SyncStrategyConfig {
        &self.strategy_config
    }

    /// 更新策略配置
    pub fn update_strategy_config(&mut self, config: SyncStrategyConfig) {
        self.strategy_config = config;
    }
}

impl Drop for SyncManager {
    fn drop(&mut self) {
        // 停止自动同步
        if let Ok(handle_guard) = self.auto_sync_handle.try_lock() {
            if let Some(handle) = handle_guard.as_ref() {
                handle.abort();
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 创建测试用的同步记录
    fn create_test_record(id: &str, username: &str) -> SyncRecord {
        SyncRecord::builder(
            format!("cred_{}", id),
            CredentialType::Login,
            OperationType::Create,
            "test_device".to_string(),
        )
        .data(
            serde_json::to_string(&LoginCredential {
                name: "Test Login".to_string(),
                username: username.to_string(),
                password: "password123".to_string(),
                url: Some("https://example.com".to_string()),
                notes: None,
                tags: vec!["test".to_string()],
                folder_id: None,
                favorite: false,
                custom_fields: HashMap::new(),
                password_history: Vec::new(),
                last_used: None,
            })
            .unwrap(),
        )
        .build()
        .unwrap()
    }

    #[tokio::test]
    async fn test_sync_manager_creation() {
        let manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        assert_eq!(manager.get_config().device_id, "test_device");
    }

    #[tokio::test]
    async fn test_sync_manager_initialization() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        assert!(manager.initialize().await.is_ok());
    }

    #[tokio::test]
    async fn test_add_and_get_record() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        manager.initialize().await.unwrap();

        let record = create_test_record("test1", "user1");
        let record_id = manager.add_record(record.clone()).await.unwrap();

        let retrieved = manager.get_record(&record_id).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().credential_id, record.credential_id);
    }

    #[tokio::test]
    async fn test_update_record() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        manager.initialize().await.unwrap();

        let record = create_test_record("test1", "user1");
        let record_id = manager.add_record(record.clone()).await.unwrap();

        let mut updated_record = record.clone();
        updated_record.version += 1;

        let success = manager
            .update_record(&record_id, updated_record)
            .await
            .unwrap();
        assert!(success);

        let retrieved = manager.get_record(&record_id).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().version, record.version + 1);
    }

    #[tokio::test]
    async fn test_delete_record() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        manager.initialize().await.unwrap();

        let record = create_test_record("test1", "user1");
        let record_id = manager.add_record(record).await.unwrap();

        let success = manager.delete_record(&record_id).await.unwrap();
        assert!(success);

        let retrieved = manager.get_record(&record_id).await.unwrap();
        assert!(retrieved.is_none());
    }

    #[tokio::test]
    async fn test_bidirectional_sync() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        manager.initialize().await.unwrap();

        let local_record = create_test_record("test1", "user1");
        manager.add_record(local_record).await.unwrap();

        let remote_records = vec![
            create_test_record("test2", "user2"),
            create_test_record("test3", "user3"),
        ];

        let result = manager
            .sync_with_remote(remote_records, SyncDirection::Bidirectional)
            .await
            .unwrap();
        assert!(result.success);
        assert!(result.synced_count > 0);
    }

    #[tokio::test]
    async fn test_sync_status() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        manager.initialize().await.unwrap();

        let status = manager.get_sync_status().await;
        assert!(!status.is_syncing);
        assert!(status.current_session.is_none());
    }

    #[tokio::test]
    async fn test_sync_stats() {
        let mut manager = SyncManager::with_default_config("test_device".to_string()).unwrap();
        manager.initialize().await.unwrap();

        let stats = manager.get_sync_stats().await;
        assert_eq!(stats.total_syncs, 0);
        assert_eq!(stats.successful_syncs, 0);
    }

    #[test]
    fn test_sync_direction() {
        assert_eq!(SyncDirection::Bidirectional, SyncDirection::Bidirectional);
        assert_ne!(SyncDirection::PushOnly, SyncDirection::PullOnly);
    }

    #[test]
    fn test_session_status() {
        assert_eq!(SessionStatus::InProgress, SessionStatus::InProgress);
        assert_ne!(SessionStatus::Completed, SessionStatus::Failed);
    }

    #[test]
    fn test_strategy_config() {
        let config = SyncStrategyConfig::default();
        assert_eq!(config.conflict_resolution, MergeStrategy::AutoMerge);
        assert!(config.enable_incremental_sync);
        assert_eq!(config.batch_size, 100);
    }
}
