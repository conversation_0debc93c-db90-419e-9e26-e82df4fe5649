//! 向量时钟演示程序
//! 
//! 运行命令: cargo run --example vector_clock_demo

use secure_password_lib::sync::vector_clock_example::{
    demonstrate_multi_device_sync, 
    demonstrate_password_manager_scenarios
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();

    println!("🎯 向量时钟密码管理器同步演示");
    println!("{}", "=".repeat(80));
    
    // 演示多设备同步场景
    demonstrate_multi_device_sync().await?;
    
    println!("\n");
    
    // 演示密码管理器特定场景
    demonstrate_password_manager_scenarios().await?;
    
    println!("\n🎉 演示完成！");
    println!("这个演示展示了向量时钟如何解决密码管理器的多端同步问题：");
    println!("✅ 检测事件的因果关系和并发性");
    println!("✅ 识别真正的冲突vs因果关系操作");
    println!("✅ 支持网络分区后的数据合并");
    println!("✅ 为冲突解决提供精确的信息");
    
    Ok(())
} 