/** @jsxImportSource @emotion/react */

/**
 * 密码列表网格组件
 * 使用Tailwind CSS + emotion混合方案实现样式
 * 根据设计稿完整还原密码管理界面
 * 更新为使用 Hybrid Storage 系统
 * 支持深浅色主题切换
 */

import React, { useState } from 'react';
import { Button, Dropdown, Modal, Form, theme, Tooltip } from 'antd';
import styled from '@emotion/styled';
import { css } from '@emotion/react';
import {
  PlusOutlined,
  SortAscendingOutlined,
  AppstoreOutlined,
  BarsOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
  InboxOutlined,
} from '@ant-design/icons';
import type { LoginCredentialOutput, LoginCredentialInput } from '../../types';
import { useHybridCredentials } from '../../contexts/HybridCredentialsContext';
import CredentialForm from './CredentialForm';

interface CredentialGridProps {
  credentials: LoginCredentialOutput[];
  selectedCredential: LoginCredentialOutput | null;
  onCredentialClick: (credential: LoginCredentialOutput) => void;
  viewMode: 'grid' | 'list';
}

interface CategoryTabProps {
  isActive: boolean;
  isHovered: boolean;
}

interface CredentialCardProps {
  isSelected: boolean;
  isHovered: boolean;
}

// 主题变量 - 使用相对值，将通过 useToken 获取实际颜色
const themeConfig = {
  transition: '0.2s ease-in-out',
  borderRadius: {
    sm: '6px',
    md: '8px',
    lg: '12px',
  },
  shadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
};

// 分类标签样式 - 使用主题令牌
const StyledCategoryTab = styled.div<CategoryTabProps & { token: any }>`
  padding: 8px 16px;
  border-radius: ${themeConfig.borderRadius.md};
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all ${themeConfig.transition};
  white-space: nowrap;
  
  ${props => props.isActive ? css`
    background-color: ${props.token.colorPrimary};
    color: ${props.token.colorWhite || '#ffffff'};
    box-shadow: ${themeConfig.shadow.md};
  ` : css`
    background-color: ${props.token.colorFillSecondary};
    color: ${props.token.colorTextSecondary};
    
    ${props.isHovered && css`
      background-color: ${props.token.colorFillTertiary};
      color: ${props.token.colorText};
    `}
  `}
`;

// 密码卡片样式 - 使用主题令牌
const StyledCredentialCard = styled.div<CredentialCardProps & { token: any }>`
  background: ${props => props.token.colorBgContainer};
  border: 1px solid ${props => props.token.colorBorder};
  border-radius: ${themeConfig.borderRadius.lg};
  padding: 20px;
  cursor: pointer;
  transition: all ${themeConfig.transition};
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 140px;
  position: relative;
  
  ${props => props.isSelected ? css`
    border-color: ${props.token.colorPrimary};
    box-shadow: 0 0 0 2px ${props.token.colorPrimaryBg};
    transform: translateY(-2px);
  ` : css`
    box-shadow: ${themeConfig.shadow.sm};
    
    ${props.isHovered && css`
      box-shadow: ${themeConfig.shadow.md};
      transform: translateY(-2px);
      border-color: ${props.token.colorPrimaryHover};
    `}
  `}
  
  &:active {
    transform: translateY(0);
  }
  
  .card-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity ${themeConfig.transition};
  }
  
  &:hover .card-actions {
    opacity: 1;
  }
`;

// 添加按钮样式 - 使用主题令牌
const StyledAddButton = styled.div<{ token: any }>`
  width: 40px;
  height: 40px;
  border: 2px dashed ${props => props.token.colorBorder};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all ${themeConfig.transition};
  background: ${props => props.token.colorFillAlter};
  
  &:hover {
    border-color: ${props => props.token.colorPrimary};
    background: ${props => props.token.colorPrimaryBg};
    transform: scale(1.05);
    
    .add-icon {
      color: ${props => props.token.colorPrimary};
      transform: scale(1.1);
    }
  }
  
  .add-icon {
    font-size: 24px;
    color: ${props => props.token.colorTextTertiary};
    transition: all ${themeConfig.transition};
  }
`;

// 服务图标配置
const getServiceConfig = (serviceName: string) => {
  const service = serviceName.toLowerCase();
  
  const configMap: Record<string, { icon: string; bgColor: string; textColor?: string }> = {
    'dog coin': { icon: '$', bgColor: '#f59e0b', textColor: '#ffffff' },
    'baidu': { icon: '🅱️', bgColor: '#3b82f6' },
    'douban': { icon: '豆', bgColor: '#10b981', textColor: '#ffffff' },
    'wechat': { icon: '💬', bgColor: '#22c55e' },
    'paypal': { icon: 'Pa', bgColor: '#059669', textColor: '#ffffff' },
    'bear': { icon: '🐻', bgColor: '#ef4444' },
    'adobe illustrator': { icon: 'Ai', bgColor: '#f97316', textColor: '#ffffff' },
    'linkedin': { icon: 'in', bgColor: '#0077b5', textColor: '#ffffff' },
    'onedrive': { icon: 'On', bgColor: '#8b5cf6', textColor: '#ffffff' },
    'sina': { icon: '新', bgColor: '#e60012', textColor: '#ffffff' },
    'default': { icon: '🔐', bgColor: '#6b7280' }
  };
  
  return configMap[service] || configMap.default;
};

/**
 * 格式化日期
 */
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${month}/${day} ${hours}:${minutes}`;
};

/**
 * 密码列表网格组件
 */
const CredentialGrid: React.FC<CredentialGridProps> = ({
  credentials,
  selectedCredential,
  onCredentialClick,
  viewMode,
}) => {
  const {
    addCredential,
    updateCredential,
    deleteCredential,
    toggleFavorite,
    archiveCredential,
  } = useHybridCredentials();
  const { token } = theme.useToken(); // 获取主题令牌
  const [activeCategory, setActiveCategory] = useState('全部');
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editingCredential, setEditingCredential] = useState<LoginCredentialOutput | null>(null);
  const [form] = Form.useForm();

  // 分类标签配置
  const categories = [
    '全部',
    '密码类',
    '通行密钥',
    'OTP令牌',
    '数字钱包'
  ];

  // 排序选项
  const sortOptions = [
    { key: 'time', label: '按创建时间排序' },
    { key: 'name', label: '按名称排序' },
    { key: 'recent', label: '按最近使用排序' },
  ];

  /**
   * 处理分类点击
   */
  const handleCategoryClick = (category: string) => {
    setActiveCategory(category);
  };

  /**
   * 处理添加新项目
   */
  const handleAddNew = () => {
    form.resetFields();
    setIsAddModalVisible(true);
  };

  /**
   * 处理编辑凭据
   */
  const handleEdit = (credential: LoginCredentialOutput, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingCredential(credential);
    form.setFieldsValue({
      name: credential.name,
      username: credential.username,
      password: credential.password,
      website: credential.website,
      notes: credential.notes,
      favorite: credential.favorite,
    });
    setIsEditModalVisible(true);
  };

  /**
   * 处理删除凭据
   */
  const handleDelete = (credential: LoginCredentialOutput, e: React.MouseEvent) => {
    e.stopPropagation();
    
    Modal.confirm({
      title: '删除凭据',
      content: (
        <div>
          <p>确定要删除凭据 "<strong>{credential.name}</strong>" 吗？</p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            删除后凭据将放入回收站，30天后会被永久删除。
          </p>
        </div>
      ),
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await deleteCredential(credential.id);
        } catch (error) {
          console.error('删除凭据失败:', error);
        }
      },
    });
  };

  /**
   * 处理封存凭据
   */
  const handleArchive = (id: number, name: string) => {
    Modal.confirm({
      title: '封存凭据',
      content: (
        <div>
          <p>确定要封存凭据 "<strong>{name}</strong>" 吗？</p>
          <p style={{ color: '#666', fontSize: '14px' }}>
            封存后凭据将从主界面隐藏，但仍然安全保存，您可以随时在封存页面恢复。
          </p>
        </div>
      ),
      okText: '封存',
      cancelText: '取消',
      onOk: async () => {
        try {
          await archiveCredential(id);
        } catch (error) {
          console.error('封存凭据失败:', error);
        }
      },
    });
  };

  /**
   * 处理切换收藏
   */
  const handleToggleFavorite = async (credential: LoginCredentialOutput, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await toggleFavorite(credential.id, credential.favorite);
    } catch (error) {
      console.error('切换收藏状态失败:', error);
    }
  };

  /**
   * 处理表单提交
   */
  const handleFormSubmit = async (credentialData: LoginCredentialInput) => {
    try {
      if (editingCredential) {
        // 更新凭据
        await updateCredential(editingCredential.id, credentialData);
        setIsEditModalVisible(false);
        setEditingCredential(null);
      } else {
        // 添加新凭据
        await addCredential(credentialData);
        setIsAddModalVisible(false);
      }
      
      form.resetFields();
    } catch (error) {
      console.error('保存凭据失败:', error);
    }
  };

  /**
   * 渲染分类标签
   */
  const renderCategoryTabs = () => (
    <div className="flex items-center">
      <div 
        className="text-sm shrink-0 mr-2"
        style={{ color: token.colorTextSecondary }}
      >
        登录类型
      </div>
      <div className="flex items-center space-x-2 overflow-x-auto">
        {categories.map(category => (
          <StyledCategoryTab
            key={category}
            isActive={activeCategory === category}
            isHovered={hoveredCategory === category}
            token={token}
            onClick={() => handleCategoryClick(category)}
            onMouseEnter={() => setHoveredCategory(category)}
            onMouseLeave={() => setHoveredCategory(null)}
          >
            {category}
          </StyledCategoryTab>
        ))}
      </div>
    </div>
  );

  /**
   * 渲染工具栏
   */
  const renderToolbar = () => (
    <div className="flex items-center justify-between mb-6">
      {/* 左侧：添加按钮 */}
      <StyledAddButton token={token} onClick={handleAddNew}>
        <PlusOutlined className="add-icon" />
      </StyledAddButton>

      {/* 右侧：操作按钮 */}
      <div className="flex items-center space-x-4">
        {/* 统计信息 */}
        <div 
          className="text-sm"
          style={{ color: token.colorTextSecondary }}
        >
          已全部加载，共{credentials.length}项
        </div>

        {/* 排序按钮 */}
        <Dropdown
          menu={{
            items: sortOptions.map(option => ({
              key: option.key,
              label: option.label,
            })),
          }}
          trigger={['click']}
        >
          <Button 
            type="text" 
            icon={<SortAscendingOutlined />}
            style={{ color: token.colorTextSecondary }}
          >
            按创建时间排序
          </Button>
        </Dropdown>

        {/* 视图切换 */}
        <div 
          className="flex items-center rounded-md p-1"
          style={{ backgroundColor: token.colorFillSecondary }}
        >
          <Button
            type={viewMode === 'list' ? 'primary' : 'text'}
            size="small"
            icon={<BarsOutlined />}
            className="border-0"
            style={{ 
              backgroundColor: viewMode === 'list' ? token.colorTextSecondary : 'transparent',
              color: viewMode === 'list' ? token.colorBgContainer : token.colorTextSecondary
            }}
          />
          <Button
            type={viewMode === 'grid' ? 'primary' : 'text'}
            size="small"
            icon={<AppstoreOutlined />}
            className="border-0 ml-1"
            style={{ 
              backgroundColor: viewMode === 'grid' ? token.colorTextSecondary : 'transparent',
              color: viewMode === 'grid' ? token.colorBgContainer : token.colorTextSecondary
            }}
          />
        </div>
      </div>
    </div>
  );

  /**
   * 渲染密码卡片
   */
  const renderCredentialCard = (credential: LoginCredentialOutput) => {
    const isSelected = selectedCredential?.id === credential.id;
    const isHovered = hoveredCard === credential.id;
    const serviceConfig = getServiceConfig(credential.name);
    const formattedDate = formatDate(credential.created_at);

    return (
      <StyledCredentialCard
        key={credential.id}
        isSelected={isSelected}
        isHovered={isHovered}
        token={token}
        onClick={() => onCredentialClick(credential)}
        onMouseEnter={() => setHoveredCard(credential.id)}
        onMouseLeave={() => setHoveredCard(null)}
      >
        {/* 卡片操作按钮 */}
        <div className="card-actions">
          <Button
            type="text"
            size="small"
            icon={credential.favorite ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
            onClick={(e) => handleToggleFavorite(credential, e)}
          />
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={(e) => handleEdit(credential, e)}
          />
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(credential, e);
              }}
            />
          </Tooltip>
          <Tooltip title="封存">
            <Button
              type="text"
              icon={<InboxOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleArchive(credential.id, credential.name);
              }}
            />
          </Tooltip>
        </div>

        {/* 服务图标 */}
        <div 
          className="w-16 h-16 rounded-xl flex items-center justify-center text-white font-bold text-xl mb-3"
          style={{ 
            backgroundColor: serviceConfig.bgColor,
            color: serviceConfig.textColor || '#ffffff'
          }}
        >
          {serviceConfig.icon}
        </div>

        {/* 用户名 */}
        <div 
          className="text-sm font-medium mb-1 truncate w-full"
          style={{ color: token.colorText }}
        >
          {credential.username || credential.name}
        </div>

        {/* 创建时间 */}
        <div 
          className="text-xs"
          style={{ color: token.colorTextSecondary }}
        >
          {formattedDate}
        </div>

        {/* 收藏标识 */}
        {credential.favorite && (
          <div className="absolute top-2 left-2">
            <StarFilled style={{ color: '#faad14', fontSize: '12px' }} />
          </div>
        )}
      </StyledCredentialCard>
    );
  };

  return (
    <div 
      className="h-full flex flex-col"
      style={{ backgroundColor: token.colorBgContainer }}
    >
      {/* 顶部分类标签 */}
      <div 
        className="px-6 pt-6 pb-4 border-b"
        style={{ borderColor: token.colorBorderSecondary }}
      >
        {renderCategoryTabs()}
      </div>

      {/* 工具栏 */}
      <div className="px-6 pt-6">
        {renderToolbar()}
      </div>

      {/* 密码网格 */}
      <div className="flex-1 px-6 py-3 overflow-auto">
        {credentials.length > 0 ? (
          <>
            <div className="grid gap-4 mb-8" style={{ gridTemplateColumns: 'repeat(auto-fill, minmax(135px, 1fr))' }}>
              {credentials.map(credential => renderCredentialCard(credential))}
            </div>
            
            {/* 底部提示 */}
            <div className="text-center py-4">
              <div 
                className="text-sm"
                style={{ color: token.colorTextTertiary }}
              >
                没有更多了
              </div>
            </div>
          </>
        ) : (
          /* 空状态 */
          <div 
            className="flex flex-col items-center justify-center h-full"
            style={{ color: token.colorTextTertiary }}
          >
            <div className="text-6xl mb-4">🔍</div>
            <div className="text-lg font-medium mb-2">没有找到匹配的项目</div>
            <div className="text-sm">尝试调整搜索条件或添加新的密码项目</div>
          </div>
        )}
      </div>

      {/* 添加凭据模态框 */}
      <Modal
        title="添加新凭据"
        open={isAddModalVisible}
        footer={null}
        onCancel={() => {
          setIsAddModalVisible(false);
          form.resetFields();
        }}
        width={600}
        destroyOnClose
      >
        <CredentialForm
          form={form}
          onFinish={handleFormSubmit}
          onCancel={() => {
            setIsAddModalVisible(false);
            form.resetFields();
          }}
          isEditing={false}
        />
      </Modal>

      {/* 编辑凭据模态框 */}
      <Modal
        title="编辑凭据"
        open={isEditModalVisible}
        footer={null}
        onCancel={() => {
          setIsEditModalVisible(false);
          setEditingCredential(null);
          form.resetFields();
        }}
        width={600}
        destroyOnClose
      >
        <CredentialForm
          form={form}
          initialValues={editingCredential || undefined}
          onFinish={handleFormSubmit}
          onCancel={() => {
            setIsEditModalVisible(false);
            setEditingCredential(null);
            form.resetFields();
          }}
          isEditing={true}
        />
      </Modal>
    </div>
  );
};

export default CredentialGrid; 