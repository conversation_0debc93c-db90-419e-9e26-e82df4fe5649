import { useState, useEffect } from 'react';
import { Modal, Descriptions, Button, Tooltip, message, Typography, Space } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined, CopyOutlined } from '@ant-design/icons';
import type { CredentialOutput, CustomField } from '../types';
import { writeText } from '@tauri-apps/plugin-clipboard-manager';

const { Text, Paragraph } = Typography;

interface CredentialDetailModalProps {
    visible: boolean;
    credential: CredentialOutput | null;
    onCancel: () => void;
}

// Helper function to copy text
const copyToClipboard = async (text: string, fieldName: string) => {
    try {
        await writeText(text);
        message.success(`${fieldName} copied to clipboard!`);
    } catch (err) {
        message.error(`Failed to copy ${fieldName}.`);
        console.error("Clipboard error:", err);
    }
};

export function CredentialDetailModal({ visible, credential, onCancel }: CredentialDetailModalProps) {
    const [showPassword, setShowPassword] = useState<Record<string, boolean>>({}); // State to track visibility for multiple passwords

    // Reset password visibility when modal opens or credential changes
    useEffect(() => {
        if (visible) {
            setShowPassword({});
        }
    }, [visible, credential]);

    if (!credential) {
        return null;
    }

    const togglePasswordVisibility = (fieldKey: string) => {
        setShowPassword(prev => ({ ...prev, [fieldKey]: !prev[fieldKey] }));
    };

    // Function to render a field with copy and show/hide functionality
    const renderSensitiveField = (label: string, value: string, fieldKey: string) => {
        const isVisible = showPassword[fieldKey] || false;
        return (
            <Descriptions.Item label={label} span={3}>
                <Space align="center">
                    <Text style={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                        {isVisible ? value : '********'}
                    </Text>
                    <Tooltip title={isVisible ? "Hide" : "Show"}>
                        <Button
                            type="text"
                            icon={isVisible ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                            onClick={() => togglePasswordVisibility(fieldKey)}
                            size="small"
                        />
                    </Tooltip>
                    <Tooltip title="Copy">
                        <Button
                            type="text"
                            icon={<CopyOutlined />}
                            onClick={() => copyToClipboard(value, label)}
                            size="small"
                        />
                    </Tooltip>
                </Space>
            </Descriptions.Item>
        );
    };

    let parsedCustomFields: CustomField[] = [];
    if (credential.custom_fields) {
        try {
            // Assuming custom_fields is already an array based on previous context
            parsedCustomFields = Array.isArray(credential.custom_fields) ? credential.custom_fields : [];
        } catch (e) {
            console.error("Error processing custom fields:", e);
            // Handle error, maybe show a message
        }
    }

    return (
        <Modal
            title="Credential Details"
            open={visible}
            onCancel={onCancel}
            footer={[
                <Button key="close" onClick={onCancel}>
                    Close
                </Button>,
            ]}
            width={600} // Adjust width as needed
            destroyOnClose // Reset state when closed
        >
            <Descriptions bordered column={1} size="small">
                <Descriptions.Item label="Service Name" span={1}>{credential.service_name}</Descriptions.Item>
                <Descriptions.Item label="Username / Email" span={1}>
                    <Space align="center">
                        <Text>{credential.username}</Text>
                        <Tooltip title="Copy Username">
                            <Button
                                type="text"
                                icon={<CopyOutlined />}
                                onClick={() => copyToClipboard(credential.username, 'Username')}
                                size="small"
                            />
                        </Tooltip>
                    </Space>
                </Descriptions.Item>

                {/* Main Password */}
                {renderSensitiveField('Password', credential.password, 'main_password')}

                {credential.notes && (
                    <Descriptions.Item label="Notes" span={1}>
                        <Paragraph style={{ marginBottom: 0 }}>{credential.notes}</Paragraph>
                    </Descriptions.Item>
                )}

                {/* Custom Fields */}
                {parsedCustomFields.length > 0 && (
                    <Descriptions.Item label="Custom Fields" span={1} labelStyle={{ paddingTop: '10px' }}>
                        <Descriptions bordered column={1} size="small" style={{ marginTop: '5px' }}>
                            {parsedCustomFields.map((field, index) => {
                                const fieldKey = `custom_${index}`;
                                if (field.type === 'password') {
                                    return renderSensitiveField(field.title, field.value, fieldKey);
                                } else if (field.type === 'textarea') {
                                    return (
                                        <Descriptions.Item key={fieldKey} label={field.title} span={1}>
                                            <Paragraph style={{ marginBottom: 0 }}>{field.value}</Paragraph>
                                        </Descriptions.Item>
                                    );
                                } else {
                                    return (
                                        <Descriptions.Item key={fieldKey} label={field.title} span={1}>
                                            <Space align="center">
                                                <Text>{field.value}</Text>
                                                <Tooltip title={`Copy ${field.title}`}>
                                                    <Button
                                                        type="text"
                                                        icon={<CopyOutlined />}
                                                        onClick={() => copyToClipboard(field.value, field.title)}
                                                        size="small"
                                                    />
                                                </Tooltip>
                                            </Space>
                                        </Descriptions.Item>
                                    );
                                }
                            })}
                        </Descriptions>
                    </Descriptions.Item>
                )}

                <Descriptions.Item label="Last Updated" span={1}>{new Date(credential.updated_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="Created" span={1}>{new Date(credential.created_at).toLocaleString()}</Descriptions.Item>
            </Descriptions>
        </Modal>
    );
}